#!/usr/bin/env python3
import argparse
import os
import sys
import yaml
from kubernetes import client
from x2robot_infer.k8s.managers import DeploymentManager


def detect_operation_type(manager, config_data):
    """自动检测是部署新模型还是更新现有模型"""
    deploy_name = config_data["name"]
    namespace = config_data.get("namespace", "default")
    
    try:
        # 尝试获取现有部署
        manager.apps_v1.read_namespaced_deployment(
            name=deploy_name, 
            namespace=namespace
        )
        return "update"
    except client.rest.ApiException as e:
        if e.status == 404:
            return "deploy"
        raise


def main():
    parser = argparse.ArgumentParser(description="Kubernetes模型部署/更新工具")
    parser.add_argument("-c", "--config", required=True, help="模型配置文件路径")
    parser.add_argument("-t", "--timeout", type=int, default=300, 
                       help="操作超时时间（秒）")
    parser.add_argument("--force-deploy", action="store_true",
                       help="强制进行新部署（即使已存在）")
    parser.add_argument("--force-update", action="store_true",
                       help="强制进行更新（即使不存在）")

    args = parser.parse_args()

    try:
        # 加载配置
        if not os.path.exists(args.config):
            raise FileNotFoundError(f"配置文件不存在: {args.config}")
            
        with open(args.config) as f:
            config_data = yaml.safe_load(f)

        manager = DeploymentManager()
        
        # 确定操作类型
        if args.force_deploy:
            operation = "deploy"
        elif args.force_update:
            operation = "update"
        else:
            operation = detect_operation_type(manager, config_data)

        # 执行相应操作
        if operation == "deploy":
            print("\n🔧 检测到新模型部署场景")
            deployment = manager.create_deployment(config_data)
            service = manager.create_service(config_data)
            manager.deploy_resources(deployment, service)
            
            # 健康检查
            if not manager.health_check(config_data, args.timeout):
                exit(1)
            print("\n✅ 模型部署完成")
            
        elif operation == "update":
            print("\n🔄 检测到模型更新场景")
            if not manager.update_deployment_and_wait(config_data, args.timeout):
                exit(1)
            print("\n✅ 模型更新完成")

    except Exception as e:
        print(f"\n❌ 发生错误: {str(e)}")
        exit(sys.stderr.write(f"操作执行失败"))


if __name__ == "__main__":
    main()