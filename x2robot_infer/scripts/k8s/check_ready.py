#!/usr/bin/env python3
import argparse
import os
import yaml
import time
from kubernetes import client, config
from kubernetes.stream import stream

def check_service_readiness(namespace, service_name):
    """检查Service的Endpoint是否就绪"""
    v1 = client.CoreV1Api()
    
    try:
        # 获取Endpoint对象
        endpoint = v1.read_namespaced_endpoints(
            name=service_name,
            namespace=namespace
        )
        
        # 检查是否有可用的Endpoint
        if not endpoint.subsets:
            return False
            
        # 检查所有端口和地址是否就绪
        for subset in endpoint.subsets:
            if not subset.addresses:
                return False
            if not subset.ports:
                return False
                
        return True
        
    except client.rest.ApiException as e:
        if e.status == 404:
            return False
        raise

def check_pod_backoff(namespace, deployment_name):
    """检查Deployment的Pod是否处于Backoff状态"""
    v1 = client.CoreV1Api()
    app_v1 = client.AppsV1Api()
    
    try:
        # 获取Deployment的selector
        deployment = app_v1.read_namespaced_deployment(
            name=deployment_name,
            namespace=namespace
        )
        selector = deployment.spec.selector.match_labels
        
        # 构造标签选择器
        label_selector = ",".join(
            [f"{k}={v}" for k, v in selector.items()]
        )
        
        # 获取关联Pod
        pods = v1.list_namespaced_pod(
            namespace=namespace,
            label_selector=label_selector
        )
        
        backoff_pods = []
        for pod in pods.items:
            # 检查容器状态
            for container_status in pod.status.container_statuses or []:
                # 检测 CrashLoopBackOff 和 ImagePullBackOff
                waiting_state = container_status.state.waiting
                if waiting_state and "BackOff" in waiting_state.reason:
                    backoff_pods.append((
                        pod.metadata.name,
                        waiting_state.reason,
                        container_status.name
                    ))
        
        return backoff_pods
        
    except client.rest.ApiException as e:
        if e.status == 404:
            return []
        raise

def get_pod_logs(namespace, pod_name, container_name):
    """获取指定Pod的日志"""
    v1 = client.CoreV1Api()
    try:
        return v1.read_namespaced_pod_log(
            name=pod_name,
            namespace=namespace,
            container=container_name,
            tail_lines=50  # 获取最后50行日志
        )
    except client.rest.ApiException as e:
        return f"无法获取日志: {e.reason}"

def main():
    parser = argparse.ArgumentParser(description="Kubernetes服务健康检查工具")
    
    # 互斥参数组：配置文件或直接指定
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("-c", "--config", help="部署配置文件路径")
    group.add_argument("-n", "--namespace", help="直接指定命名空间")
    
    # 参数验证规则
    def validate_name(value):
        if not value.isalnum() or '-' not in value:
            raise argparse.ArgumentTypeError("Name必须包含字母数字和横线")
        return value
    
    parser.add_argument("-r", "--name", 
                       help="部署名称（使用直接指定方式时必需）",
                       type=validate_name,
                       required=False)
    parser.add_argument("-t", "--timeout", 
                       help="检查超时时间（秒）", 
                       type=int,
                       default=300)
    parser.add_argument("-l", "--log-level",
                       help="日志输出级别",
                       choices=["debug", "info", "error"],
                       default="info")

    args = parser.parse_args()
    
    # 初始化Kubernetes客户端
    config.load_kube_config()
    
    # 确定要检查的部署
    if args.config:
        # 配置文件模式
        if not os.path.exists(args.config):
            print("错误：配置文件不存在")
            exit(1)
            
        with open(args.config, 'r') as f:
            config_data = yaml.safe_load(f)
            
        deploy_name = config_data['name']
        namespace = config_data.get('namespace', 'default')
        service_name = f"{deploy_name}-service"
    else:
        # 直接指定模式
        if not args.name or not args.namespace:
            print("错误：直接模式需要--name和--namespace参数")
            exit(1)
        deploy_name = args.name
        namespace = args.namespace
        service_name = f"{deploy_name}-service"

    start_time = time.time()
    timeout = args.timeout
    last_log_time = 0
    
    while time.time() - start_time < timeout:
        # 检查服务状态
        service_ready = check_service_readiness(namespace, service_name)
        
        # 检查Pod状态
        backoff_pods = check_pod_backoff(namespace, deploy_name)
        
        # 输出进度信息
        if time.time() - last_log_time > 5 or args.log_level == "debug":  # 每5秒输出一次
            print(f"[{time.strftime('%H:%M:%S')}] 服务状态: {'Ready' if service_ready else 'NotReady'} | 问题Pod数量: {len(backoff_pods)}")
            last_log_time = time.time()
        
        # 如果检测到Backoff Pod
        if backoff_pods:
            print("\n检测到异常Pod：")
            for pod_name, reason, container_name in backoff_pods:
                print(f" - Pod: {pod_name} | 容器: {container_name} | 状态: {reason}")
                logs = get_pod_logs(namespace, pod_name, container_name)
                print("\n日志内容（最后50行）：")
                print("="*50)
                print(logs)
                print("="*50 + "\n")
            exit(2)
        
        # 如果服务就绪则退出
        if service_ready:
            print("\n服务已就绪！")
            exit(0)
            
        time.sleep(1)
    
    # 超时处理
    print(f"\n超时：超过 {timeout} 秒服务仍未就绪")
    exit(1)

if __name__ == "__main__":
    main()