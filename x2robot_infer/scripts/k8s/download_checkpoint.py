import os
import re
import stat
import requests
import logging
import time
import hashlib
import paramiko
from pathlib import Path
from scp import SCPClient
from urllib.parse import urlparse
import json

# 配置日志格式
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# ====================== 数据结构定义 ======================
class ScpFileInfo:
    """SCP文件元信息容器"""
    def __init__(self, size=0, mtime=0):
        self.size = size
        self.mtime = mtime

# ====================== 进度管理类 ======================
class ScpProgress:
    """单个文件下载进度管理器"""
    def __init__(self, remote_path, total_size):
        self.filename = os.path.basename(remote_path)
        self.total_size = total_size
        self.start_time = time.time()
        self.last_print_time = self.start_time

    def update(self, filename, size, sent):
        """SCPClient回调函数"""
        current_time = time.time()
        elapsed = current_time - self.start_time
        speed = sent / (1024 * 1024 * elapsed) if elapsed > 0 else 0

        # 控制刷新频率
        if current_time - self.last_print_time < 0.5 and sent != self.total_size:
            return

        if self.total_size > 0:
            percent = sent / self.total_size * 100
            remaining = (self.total_size - sent) / (1024 * 1024 * speed) if speed > 0 else 0
            progress_msg = (
                f"{self.filename} | {percent:.1f}% | "
                f"{speed:.2f} MB/s | "
                f"{sent/(1024 * 1024):.2f}MB / {self.total_size/(1024 * 1024):.2f}MB | "
                f"ETA: {remaining:.1f}s"
            )
        else:
            progress_msg = (
                f"{self.filename} | {sent/(1024 * 1024):.2f}MB | "
                f"{speed:.2f} MB/s"
            )

        logging.info(progress_msg)
        self.last_print_time = current_time


class DirectoryProgress:
    """目录下载进度管理器"""
    def __init__(self, files_info: list):
        self.files_info = files_info  # [(rel_path, size), ...]
        self.total_files = len(files_info)
        self.total_size = sum(size for _, size in files_info)
        self.current_file_index = 0
        self.current_file_size = files_info[0][1] if files_info else 0
        self.current_file_downloaded = 0
        self.total_downloaded = 0
        self.start_time = time.time()
        self.last_print_time = self.start_time
        
    def set_current_file(self, index: int):
        """设置当前下载的文件索引"""
        self.current_file_index = index
        self.current_file_downloaded = 0
        if index < len(self.files_info):
            self.current_file_size = self.files_info[index][1]
        else:
            self.current_file_size = 0
        
    def __call__(self, filename, size, sent):
        """使对象可调用，作为SCPClient的回调函数"""
        return self.update(filename, size, sent)
        
    def update(self, filename, size, sent):
        """SCP回调函数"""
        # 更新当前文件已下载量
        self.current_file_downloaded = sent
        
        # 计算总体进度
        previous_files_size = sum(
            size for _, size in self.files_info[:self.current_file_index]
        )
        self.total_downloaded = previous_files_size + sent
        
        current_time = time.time()
        elapsed = current_time - self.start_time
        
        # 控制刷新频率
        if current_time - self.last_print_time < 0.5 and sent != size:
            return
            
        speed = self.total_downloaded / (1024 * 1024 * elapsed) if elapsed > 0 else 0
        
        # 当前文件进度
        file_progress = (sent / self.current_file_size * 100) if self.current_file_size > 0 else 0
        
        # 总体进度
        total_progress = (self.total_downloaded / self.total_size * 100) if self.total_size > 0 else 0
        
        # 剩余时间估算
        remaining_bytes = self.total_size - self.total_downloaded
        remaining_time = remaining_bytes / (1024 * 1024 * speed) if speed > 0 else 0
        
        current_file_name = os.path.basename(self.files_info[self.current_file_index][0])
        
        progress_msg = (
            f"[{self.current_file_index + 1}/{self.total_files}] {current_file_name} | "
            f"文件: {file_progress:.1f}% | "
            f"总体: {total_progress:.1f}% | "
            f"速度: {speed:.2f} MB/s | "
            f"总进度: {self.total_downloaded/(1024 * 1024):.2f}MB / {self.total_size/(1024 * 1024):.2f}MB | "
            f"ETA: {remaining_time:.1f}s"
        )
        
        logging.info(progress_msg)
        self.last_print_time = current_time

# ====================== 工具函数 ======================
def calculate_file_hash(file_path: str, algorithm: str = 'sha256') -> str:
    """计算文件的Hash值"""
    hash_obj = hashlib.new(algorithm)
    try:
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_obj.update(chunk)
        return hash_obj.hexdigest()
    except Exception as e:
        logging.error(f"计算文件Hash失败 {file_path}: {str(e)}")
        return None

def calculate_remote_file_hash(ssh: paramiko.SSHClient, remote_path: str, algorithm: str = 'sha256') -> str:
    """计算远程文件的Hash值"""
    try:
        stdin, stdout, stderr = ssh.exec_command(f"{algorithm}sum '{remote_path}'")
        output = stdout.read().decode().strip()
        error = stderr.read().decode().strip()
        
        if error:
            logging.error(f"远程Hash计算错误: {error}")
            return None
            
        # 解析输出格式: "hash_value  filename"
        hash_value = output.split()[0]
        return hash_value
    except Exception as e:
        logging.error(f"计算远程文件Hash失败 {remote_path}: {str(e)}")
        return None

def parse_scp_path(scp_path: str) -> tuple:
    """解析SCP路径格式 (user@host:path)"""
    match = re.match(r"^(?:([a-zA-Z0-9_-]+)@)?([^:]+):(.+)$", scp_path)
    if not match:
        raise ValueError(f"Invalid SCP path format: {scp_path}")
    user, host, path = match.groups()
    return user or "root", host, path

def get_remote_file_size(url):
    """获取远程文件的大小"""
    try:
        response = requests.head(url, allow_redirects=True)
        if response.status_code == 200:
            return int(response.headers.get('content-length', 0))
        return None
    except Exception as e:
        logging.error(f"获取远程文件大小失败: {str(e)}")
        return None

def is_remote_directory(ssh: paramiko.SSHClient, remote_path: str) -> bool:
    """检查远程路径是否为目录"""
    try:
        stdin, stdout, stderr = ssh.exec_command(f"test -d '{remote_path}' && echo 'DIR' || echo 'FILE'")
        output = stdout.read().decode().strip()
        return output == 'DIR'
    except Exception:
        return False

# ====================== 目录结构处理 ======================
def get_remote_directory_structure(ssh: paramiko.SSHClient, remote_path: str) -> dict:
    """获取远程目录结构和文件Hash信息"""
    try:
        logging.info(f"开始扫描远程目录: {remote_path}")
        stdin, stdout, stderr = ssh.exec_command(f"find '{remote_path}' -type f")
        output = stdout.read().decode().strip()
        error = stderr.read().decode().strip()
        
        if error:
            logging.error(f"获取远程目录结构失败: {error}")
            return None
            
        files = [f for f in output.split('\n') if f.strip()]
        total_files = len(files)
        logging.info(f"找到 {total_files} 个文件")
        
        file_hashes = {}
        
        for i, file_path in enumerate(files, 1):
            # 计算相对路径
            rel_path = os.path.relpath(file_path, remote_path)
            
            logging.info(f"({i}/{total_files}) 计算文件哈希: {rel_path}")
            file_hash = calculate_remote_file_hash(ssh, file_path)
            
            if file_hash:
                file_hashes[rel_path] = file_hash
                logging.debug(f"文件 '{rel_path}' 哈希值: {file_hash}")
            else:
                logging.warning(f"无法计算文件 '{rel_path}' 的哈希值")
        
        logging.info(f"成功获取 {len(file_hashes)}/{total_files} 个文件的哈希值")
        return file_hashes
        
    except Exception as e:
        logging.error(f"获取远程目录结构失败: {str(e)}")
        return None

def get_local_directory_structure(local_path: str) -> dict:
    """获取本地目录结构和文件Hash信息"""
    file_hashes = {}
    try:
        if os.path.isfile(local_path):
            logging.info(f"开始处理单个文件: {local_path}")
            file_hash = calculate_file_hash(local_path)
            
            if file_hash:
                file_hashes[os.path.basename(local_path)] = file_hash
                logging.info(f"文件哈希计算完成: {file_hash}")
            else:
                logging.warning(f"无法计算文件哈希: {local_path}")
                
            return file_hashes
            
        elif os.path.isdir(local_path):
            logging.info(f"开始扫描本地目录: {local_path}")
            all_files = []
            for root, _, files in os.walk(local_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, local_path)
                    all_files.append((file_path, rel_path))
            
            total_files = len(all_files)
            logging.info(f"找到 {total_files} 个文件")
            
            for i, (file_path, rel_path) in enumerate(all_files, 1):
                logging.info(f"({i}/{total_files}) 开始计算文件哈希: {rel_path}")
                file_hash = calculate_file_hash(file_path)
                
                if file_hash:
                    file_hashes[rel_path] = file_hash
                    logging.debug(f"文件 '{rel_path}' 哈希值: {file_hash}")
                else:
                    logging.warning(f"无法计算文件 '{rel_path}' 的哈希值")
            
            logging.info(f"成功获取 {len(file_hashes)}/{total_files} 个文件的哈希值")
            return file_hashes
            
        else:
            logging.error(f"路径不存在: {local_path}")
            return {}
            
    except Exception as e:
        logging.error(f"获取本地目录结构失败: {str(e)}")
        return {}

def compare_hash_structures(local_hashes: dict, remote_hashes: dict) -> tuple:
    """比较本地和远程的Hash结构"""
    files_to_download = []
    files_to_delete = []
    
    # 检查远程文件
    for rel_path, remote_hash in remote_hashes.items():
        if rel_path not in local_hashes:
            files_to_download.append(rel_path)
        elif local_hashes[rel_path] != remote_hash:
            files_to_download.append(rel_path)
    
    # 检查本地多余的文件
    for rel_path in local_hashes:
        if rel_path not in remote_hashes:
            files_to_delete.append(rel_path)
    
    is_match = len(files_to_download) == 0 and len(files_to_delete) == 0
    return files_to_download, files_to_delete, is_match

# ====================== 下载功能 ======================
def scp_download_file(
    host: str,
    username: str,
    remote_path: str,
    local_path: str,
    private_key_path: str = "/root/.ssh/id_rsa"
) -> bool:
    """带实时进度条的SCP下载"""
    ssh = None
    scp = None
    try:
        # 创建SSH连接
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        private_key = paramiko.RSAKey.from_private_key_file(private_key_path)
        ssh.connect(host, username=username, pkey=private_key, timeout=30)

        # 获取文件大小
        with ssh.open_sftp() as sftp:
            file_stat = sftp.stat(remote_path)
            total_size = file_stat.st_size

        # 初始化进度条
        progress = ScpProgress(remote_path, total_size)
        
        # 创建SCP客户端并设置回调
        scp = SCPClient(
            ssh.get_transport(),
            socket_timeout=300,
            progress=progress.update
        )
        
        # 执行下载
        scp.get(remote_path, local_path, recursive=False)
        
        # 最终统计
        total_time = time.time() - progress.start_time
        avg_speed = total_size / (1024 * 1024 * total_time)
        logging.info(
            f"下载完成! 平均速度: {avg_speed:.2f} MB/s | "
            f"耗时: {total_time:.1f}秒 | "
            f"文件大小: {total_size/(1024 * 1024):.2f}MB"
        )
        return True

    except Exception as e:
        logging.error(f"下载失败: {str(e)}")
        return False
    finally:
        if scp:
            scp.close()
        if ssh:
            ssh.close()

def scp_download_directory(
    host: str,
    username: str,
    remote_path: str,
    local_path: str,
    files_to_download: list,
    private_key_path: str = "/root/.ssh/id_rsa"
) -> bool:
    """批量下载目录中的指定文件"""
    ssh = None
    scp = None
    try:
        # 创建SSH连接
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        private_key = paramiko.RSAKey.from_private_key_file(private_key_path)
        ssh.connect(host, username=username, pkey=private_key, timeout=30)

        # 获取所有文件的大小信息
        files_info = []
        with ssh.open_sftp() as sftp:
            for rel_path in files_to_download:
                try:
                    remote_file_path = os.path.join(remote_path, rel_path).replace('\\', '/')
                    file_stat = sftp.stat(remote_file_path)
                    files_info.append((rel_path, file_stat.st_size))
                except Exception as e:
                    logging.error(f"获取文件信息失败 {rel_path}: {str(e)}")
                    files_info.append((rel_path, 0))

        # 创建统一的进度管理器
        directory_progress = DirectoryProgress(files_info)
        
        # 创建SCP客户端并设置回调
        scp = SCPClient(
            ssh.get_transport(),
            socket_timeout=300,
            progress=directory_progress.update
        )
        
        success_count = 0
        for i, (rel_path, file_size) in enumerate(files_info):
            try:
                remote_file_path = os.path.join(remote_path, rel_path).replace('\\', '/')
                local_file_path = os.path.join(local_path, rel_path)
                
                # 创建本地目录
                os.makedirs(os.path.dirname(local_file_path), exist_ok=True)
                
                # 设置当前文件索引
                directory_progress.set_current_file(i)
                
                # 下载文件
                scp.get(remote_file_path, local_file_path, recursive=False)
                success_count += 1
                logging.info(f"✓ 完成下载: {rel_path}")
                
            except Exception as e:
                logging.error(f"下载文件失败 {rel_path}: {str(e)}")
                continue
        
        # 最终统计
        total_time = time.time() - directory_progress.start_time
        avg_speed = directory_progress.total_downloaded / (1024 * 1024 * total_time) if total_time > 0 else 0
        
        logging.info(
            f"批量下载完成: {success_count}/{len(files_to_download)} 个文件 | "
            f"总大小: {directory_progress.total_downloaded/(1024 * 1024):.2f}MB | "
            f"平均速度: {avg_speed:.2f} MB/s | "
            f"耗时: {total_time:.1f}秒"
        )
        
        return success_count == len(files_to_download)

    except Exception as e:
        logging.error(f"批量下载失败: {str(e)}")
        return False
    finally:
        if scp:
            scp.close()
        if ssh:
            ssh.close()

def download_file(url: str, file_path: str, progress_interval: float = 1.0) -> bool:
    """从指定URL下载文件到本地路径"""
    try:
        start_time = time.time()
        last_log_time = start_time
        downloaded = 0
        
        response = requests.get(url, stream=True, timeout=30)
        response.raise_for_status()

        # 获取文件大小
        total_size = int(response.headers.get('content-length', 0))
        
        with open(file_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    
                    # 控制进度刷新频率
                    current_time = time.time()
                    if current_time - last_log_time >= progress_interval:
                        elapsed = current_time - start_time
                        speed = downloaded / (1024 * 1024 * elapsed) if elapsed > 0 else 0
                        
                        if total_size > 0:
                            progress = downloaded / total_size * 100
                            remaining = (total_size - downloaded) / (1024 * 1024 * speed) if speed > 0 else 0
                            logging.info(
                                f"进度: {progress:.1f}% | "
                                f"速度: {speed:.2f} MB/s | "
                                f"已下载: {downloaded/(1024 * 1024):.2f}MB | "
                                f"剩余时间: {remaining:.1f}s"
                            )
                        else:
                            logging.info(
                                f"已下载: {downloaded/(1024 * 1024):.2f}MB | "
                                f"速度: {speed:.2f} MB/s"
                            )
                        last_log_time = current_time

        # 最终完成日志
        total_time = time.time() - start_time
        if total_size > 0 and downloaded == total_size:
            speed = total_size / (1024 * 1024 * total_time) if total_time > 0 else 0
            logging.info(
                f"下载完成! 总大小: {total_size/(1024 * 1024):.2f}MB | "
                f"平均速度: {speed:.2f} MB/s | "
                f"耗时: {total_time:.1f}秒"
            )
        return True

    except requests.exceptions.RequestException as e:
        logging.error(f"下载失败 - 网络错误: {str(e)}")
    except IOError as e:
        logging.error(f"文件写入失败: {str(e)}")
    except Exception as e:
        logging.error(f"未知错误: {str(e)}")
    return False

# ====================== 主程序逻辑 ======================
def handle_http_download(ckpt_path: str, download_url: str) -> int:
    """处理HTTP下载逻辑"""
    # 检查本地文件Hash
    local_hash = None
    if os.path.exists(ckpt_path):
        local_hash = calculate_file_hash(ckpt_path)
        logging.info(f"本地文件Hash: {local_hash}")
    
    # 下载文件到临时位置计算Hash
    temp_path = f"{ckpt_path}.tmp"
    logging.info(f"开始HTTP下载: {download_url} => {temp_path}")
    
    if download_file(download_url, temp_path):
        remote_hash = calculate_file_hash(temp_path)
        logging.info(f"远程文件Hash: {remote_hash}")
        
        if local_hash == remote_hash:
            logging.info("文件Hash匹配，无需重新下载")
            os.remove(temp_path)
            return 0
        else:
            logging.info("文件Hash不匹配，使用新下载的文件")
            if os.path.exists(ckpt_path):
                os.remove(ckpt_path)
            os.rename(temp_path, ckpt_path)
            return 0
    else:
        if os.path.exists(temp_path):
            os.remove(temp_path)
        return 2

def handle_scp_download(ckpt_path: str, download_path: str) -> int:
    """处理SCP下载逻辑"""
    try:
        username, host, remote_path = parse_scp_path(download_path)
        
        # 建立SSH连接
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        private_key = paramiko.RSAKey.from_private_key_file("/root/.ssh/id_rsa")
        ssh.connect(hostname=host, username=username, pkey=private_key, timeout=30)
        
        # 检查远程路径类型
        is_dir = is_remote_directory(ssh, remote_path)
        
        if is_dir:
            return handle_scp_directory(ssh, ckpt_path, remote_path, host, username)
        else:
            return handle_scp_file(ssh, ckpt_path, remote_path, host, username)
            
    except Exception as e:
        logging.error(f"SCP处理失败: {str(e)}")
        return 5
    finally:
        if 'ssh' in locals():
            ssh.close()

def handle_scp_directory(ssh, ckpt_path, remote_path, host, username) -> int:
    """处理SCP目录下载"""
    logging.info(f"检测到远程目录: {remote_path}")
    
    # 获取远程目录结构
    remote_hashes = get_remote_directory_structure(ssh, remote_path)
    if remote_hashes is None:
        logging.error("无法获取远程目录结构")
        return 5
    
    # 获取本地目录结构
    local_hashes = get_local_directory_structure(ckpt_path)
    
    # 比较Hash结构
    files_to_download, files_to_delete, is_match = compare_hash_structures(
        local_hashes, remote_hashes
    )
    
    if is_match:
        logging.info("目录Hash完全匹配，无需同步")
        return 0
    
    # 删除本地多余文件
    for rel_path in files_to_delete:
        local_file_path = os.path.join(ckpt_path, rel_path)
        try:
            os.remove(local_file_path)
            logging.info(f"删除多余文件: {rel_path}")
        except Exception as e:
            logging.error(f"删除文件失败 {rel_path}: {str(e)}")
    
    # 下载需要更新的文件
    if files_to_download:
        logging.info(f"需要下载 {len(files_to_download)} 个文件")
        # 确保目标目录存在
        os.makedirs(ckpt_path, exist_ok=True)
        
        success = scp_download_directory(
            host=host,
            username=username,
            remote_path=remote_path,
            local_path=ckpt_path,
            files_to_download=files_to_download
        )
        
        if success:
            logging.info("目录同步完成")
            return 0
        else:
            return 2
    else:
        logging.info("目录同步完成（仅删除操作）")
        return 0

def handle_scp_file(ssh, ckpt_path, remote_path, host, username) -> int:
    """处理SCP文件下载"""
    logging.info(f"检测到远程文件: {remote_path}")
    
    # 计算远程文件Hash
    remote_hash = calculate_remote_file_hash(ssh, remote_path)
    if not remote_hash:
        logging.error("无法计算远程文件Hash")
        return 5
    
    # 计算本地文件Hash
    local_hash = None
    if os.path.exists(ckpt_path):
        local_hash = calculate_file_hash(ckpt_path)
    
    logging.info(f"远程文件Hash: {remote_hash}")
    logging.info(f"本地文件Hash: {local_hash}")
    
    if local_hash == remote_hash:
        logging.info("文件Hash匹配，无需重新下载")
        return 0
    
    # 创建目标目录
    os.makedirs(os.path.dirname(ckpt_path), exist_ok=True)
    
    # 执行下载
    logging.info(f"开始SCP下载: {remote_path} => {ckpt_path}")
    success = scp_download_file(
        host=host,
        username=username,
        remote_path=remote_path,
        local_path=ckpt_path
    )
    
    if success:
        return 0
    else:
        return 2

def main():
    # 获取环境变量
    ckpt_path = os.environ.get("CKPT_PATH")
    download_url = os.environ.get("DOWNLOAD_URL")
    download_path = os.environ.get("DOWNLOAD_PATH")

    # 验证环境变量
    if not ckpt_path:
        logging.error("必须设置 CKPT_PATH 环境变量")
        exit(1)
    
    if not download_url and not download_path:
        logging.error("必须设置 DOWNLOAD_URL 或 DOWNLOAD_PATH 环境变量")
        exit(1)
    
    if download_url and download_path:
        logging.error("不能同时设置 DOWNLOAD_URL 和 DOWNLOAD_PATH")
        exit(1)

    # 处理HTTP下载
    if download_url:
        exit_code = handle_http_download(ckpt_path, download_url)
        exit(exit_code)

    # 处理SCP下载
    if download_path:
        exit_code = handle_scp_download(ckpt_path, download_path)
        exit(exit_code)

if __name__ == "__main__":
    main()