import os
os.environ["CUDA_VISIBLE_DEVICES"] = "1"
device='cuda:0'

import yaml
import torch
import argparse
import numpy as np
from scipy.spatial.transform import Rotation as R

from eval.utils import interpolates_actions
from eval.robot_controller import Robot<PERSON>ontroller
from diffusion_policy.common.pytorch_util import dict_apply

from mini_dp_train.action_predictor import DP_Infer_model

def main(args):
    # with open(args.config_path, "r") as f:
    #     config = yaml.load(f, Loader=yaml.FullLoader)
    checkpoint = torch.load(args.checkpoint_path, map_location=device)
    config = checkpoint['config']
    dof_config = config.get("dof_config",{
        "follow_left_ee_cartesian_pos": 3,
        "follow_left_ee_rotation": 3,
        "follow_left_gripper": 1,
        "follow_right_ee_cartesian_pos": 3,
        "follow_right_ee_rotation": 3,
        "follow_right_gripper": 1,
        "head_actions":3,
        "height":1,
        # "car_pose":2,
    })
    # load model
    policy = DP_Infer_model(config,args.checkpoint_path,args.use_ema,args.instructions)
    n_obs_steps = policy.obs_horizon
    action_dim = policy.action_dim
    action_start_ratio = args.action_start_ratio #0.25
    action_end_ratio = args.action_end_ratio
    action_interpolate_multiplier = args.action_interpolate_multiplier #20
    print("model loaded, action dim: ", policy.action_dim)
    print("dof_config:",dof_config,flush=True)
    assert policy.action_dim == sum(dof_config.values()), f"action_dim: {policy.action_dim} != sum(dof_config.values()): {sum(dof_config.values())}"

    def _pred_func(self, views, actions) -> dict:
        action_keys = list(actions.keys())
        print("recivied actions:", action_keys, flush=True)

        left_agent_data = actions['follow1_pos'] # (7)
        right_agent_data = actions['follow2_pos'] # (7)
        if "head_pos" in actions:
            head_pos = actions["head_pos"]
        else:
            head_pos = [0.0,-1.0]
        if dof_config["head_actions"] == 3:
            head_pos = head_pos+[0.0]

        if "lifting_mechanism_position" in actions:
            height = actions["lifting_mechanism_position"]
        else:
            height = [0.0]

        if "car_pose" in actions:
            car_pose = actions["car_pose"]
        else:
            car_pose = [0.0, 0.0]

        agent_data = [left_agent_data, right_agent_data]
        if "head_actions" in dof_config:
            agent_data += [head_pos]
        if "height" in dof_config:
            agent_data += [height]
        if "car_pose" in dof_config:
            agent_data += [car_pose]
        print("agent_data:",agent_data)
        agent_data = np.concatenate(agent_data)
        print("agent_data:",agent_data.shape)

        # agent_data = np.concatenate([left_agent_data, right_agent_data])
        agent_pos = np.array(agent_data).reshape(n_obs_steps, action_dim)
        obs = {
            'face_view': views['camera_front'],
            'right_wrist_view': views['camera_right'],
            'left_wrist_view': views['camera_left'],
            'agent_pos': agent_pos,
        }

        with torch.no_grad():
            obs_dict = dict_apply(obs, 
                lambda x: torch.from_numpy(x).moveaxis(-1,1).unsqueeze(0))
            result = policy.predict_action(obs_dict)  
            action_pred = result['action_pred'][0].detach().to('cpu').numpy()
            left_action_pred = action_pred[:,:7]
            right_action_pred = action_pred[:,7:14]
            end_index = 14
            if "head_actions" in dof_config:
                head_action_pred = action_pred[:,end_index:end_index+dof_config["head_actions"]]
                end_index += dof_config["head_actions"]
                if head_action_pred.shape[-1] == 3:
                    head_action_pred = head_action_pred[:,:2]
            else:
                head_action_pred = None

            if "height" in dof_config:
                height_action_pred = action_pred[:,end_index:end_index+dof_config["height"]]
                end_index += dof_config["height"]
            else:
                height_action_pred = None

            if "car_pose" in dof_config:
                car_action_pred = action_pred[:,end_index:end_index+dof_config["car_pose"]]
                end_index += dof_config["car_pose"]
            else:
                car_action_pred = None

        inter_len = action_interpolate_multiplier*len(left_action_pred)
        left_action_pred = interpolates_actions(actions=left_action_pred, num_actions=left_action_pred.shape[0], target_num_actions=inter_len, action_dim=7)
        right_action_pred = interpolates_actions(actions=right_action_pred, num_actions=right_action_pred.shape[0], target_num_actions=inter_len, action_dim=7)
        if head_action_pred is not None:
            print("head_action_pred:",head_action_pred.shape)
            head_action_pred = interpolates_actions(actions=head_action_pred, num_actions=head_action_pred.shape[0], target_num_actions=inter_len, action_dim=2)
        if height_action_pred is not None:
            print("height_action_pred:",height_action_pred.shape)
            height_action_pred = None
            # TODO: 线性插值
            # height_action_pred = interpolates_actions(actions=height_action_pred, num_actions=height_action_pred.shape[0], target_num_actions=inter_len, action_dim=dof_config["height"])
        if car_action_pred is not None:
            car_action_pred = None
            # TODO: 速度转距离&角度 + 插值
            # car_action_pred = interpolates_actions(actions=car_action_pred, num_actions=car_action_pred.shape[0], target_num_actions=inter_len, action_dim=dof_config["car_pose"])

        start_frame = int(action_start_ratio*inter_len)
        end_frame = int(action_end_ratio*inter_len)
        left_action_pred = left_action_pred[start_frame:end_frame]
        right_action_pred = right_action_pred[start_frame:end_frame]
        follow1 = left_action_pred.tolist()
        follow2 = right_action_pred.tolist()

        serialized_actions = {
            "follow1_pos":follow1,
            "follow2_pos":follow2,
            "follow1_joints":follow1,
            "follow2_joints":follow1
        }
        if head_action_pred is not None:
            # serialized_actions["head_ops"] = head_action_pred.tolist()
            serialized_actions["head_pos"] = head_action_pred.tolist()
        else:
            # serialized_actions["head_ops"] = [[0.0,-1.0]]*len(follow1)
            serialized_actions["head_pos"] = [[0.0,-1.0]]*len(follow1)

        if height_action_pred is not None:
            serialized_actions["lifting_mechanism_position"] = height_action_pred.tolist()
        else:
            serialized_actions["lifting_mechanism_position"] = [[0.0]]*len(follow1)

        if car_action_pred is not None:
            serialized_actions["car_pose"] = car_action_pred.tolist()
        else:
            serialized_actions["car_pose"] = [[0.0,0.0]]*len(follow1)

        print("serialized_actions:",serialized_actions.keys(),flush=True)
        for k,v in serialized_actions.items():
            print(k,len(v),len(v[0]),flush=True)

        return serialized_actions
    
    RobotController.prediction = _pred_func
    robot_controller = RobotController(robot_id = args.robot_id, max_time_step=args.max_time_step)
    robot_controller.connect()
    print("robot connected")
    robot_controller.run(record_mode = args.record_mode)
    print("robot closing")
    robot_controller.close()


if __name__ == "__main__":
    # read args
    parser = argparse.ArgumentParser()
    parser.add_argument("--robot_id", type=int, default=0)
    parser.add_argument("--max_time_step", type=int, default=1000)
    parser.add_argument("--record_mode", action='store_true')

    parser.add_argument("--config_path", type=str, default=None)
    parser.add_argument("--checkpoint_path", type=str, default=None)
    parser.add_argument("--instructions", type=str, default=None)

    parser.add_argument("--use_ema", action='store_true')
    parser.add_argument("--action_start_ratio", type=float, default=0.15)
    parser.add_argument("--action_end_ratio", type=float, default=0.3) ## TODO: 默认值应该是1.0， 测试时设为0.3以便快速结束
    parser.add_argument("--action_interpolate_multiplier", type=int, default=20)

    args = parser.parse_args()

    # hang_clothes_0615_from_mix_right_mn
    # args.config_path = "/x2robot/common_projects/diffusion_policy_train/workspace/test/dp.yml"
    # args.checkpoint_path = "/home/<USER>/workspace/Diffusion_Policy/test/latest.pth"
    args.instructions = ["test test"]

    # args.use_ema = True
    args.robot_id = 1
    main(args)