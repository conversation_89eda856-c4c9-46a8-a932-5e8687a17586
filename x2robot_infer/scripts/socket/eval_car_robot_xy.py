import os
os.environ["CUDA_VISIBLE_DEVICES"] = "0,1"
device='cuda:0'

import yaml
import dill
import hydra
import torch

import time

import argparse
import numpy as np
from scipy.spatial.transform import Rotation as R

from eval.utils import interpolates_actions
# from eval.robot_controller import RobotController
from CommunicationPort.RobotController import RobotController
from diffusion_policy.common.pytorch_util import dict_apply

# from mini_dp_train.action_predictor import DP_Infer_model
from diffusion_policy.workspace.base_workspace import BaseWorkspace
from diffusion_policy.policy.base_image_policy import BaseImagePolicy
from collections import deque

class ArmActionHistory:
    def __init__(self, max_length=20):
        self.history = deque(maxlen=max_length)
    
    def add_action(self, action):
        """
        添加一个新的动作到历史记录中
        """
        self.history.append(action)
    
    def get_history(self):
        """
        获取所有的动作历史列表
        """
        hist = list(self.history)
        # print(f'hist: {hist}')
        hist = np.array(hist)
        return hist
    
    def __len__(self):
        """
        返回当前历史记录中的动作数量
        """
        return len(self.history)


def main(args):
    setting = "test"
    os.makedirs(f"saved/{setting}", exist_ok=True)
    save_path = f"saved/{setting}/"

    # load model
    ckpt_path = args.checkpoint_path
    print(f'ckpt_path:{ckpt_path}')
    payload = torch.load(open(ckpt_path, 'rb'), pickle_module=dill)
    cfg = payload['cfg']
    cls = hydra.utils.get_class(cfg._target_)
    
    rgb_keys = list()
    lowdim_keys = list()
    obs_shape_meta = cfg.shape_meta.obs
    for key, attr in obs_shape_meta.items():
        type = attr.get('type', 'low_dim')
        if type == 'rgb':
            rgb_keys.append(key)
        elif type == 'low_dim':
            lowdim_keys.append(key)

    cfg.multi_run.run_dir = '/home/<USER>/diffusion_policy/output'
    workspace = cls(cfg, cfg.multi_run.run_dir)
    workspace: BaseWorkspace
    workspace.load_payload(payload, exclude_keys=None, include_keys=None)
    policy: BaseImagePolicy
    policy = workspace.model
    policy.eval().to(device)
    low_dim_obs_horizon = cfg.task.low_dim_obs_horizon
    img_obs_horizon = cfg.task.img_obs_horizon
    is_bi_mode = cfg.task.dataset.is_bi_mode
    action_history_length = cfg.task.action_history_length if hasattr(cfg.task, 'action_history_length') else 0
    # policy.num_inference_steps = 100 # DDIM inference iterations
    # policy.n_action_steps = policy.horizon - policy.n_obs_steps + 1
    # set inference params
    shape_meta = cfg.task.shape_meta
    use_gripper_cur = cfg.task.use_gripper_cur if hasattr(cfg.task, 'use_gripper_cur') else False
    print(f'use_gripper_cur: {use_gripper_cur}')
    action_dim = shape_meta['action']['shape'][-1]
    policy.num_inference_steps = 100
    print('policy.num_inference_steps:', policy.num_inference_steps)
    # print('policy.n_action_steps:', policy.n_action_steps)
    
    history_len = 0
    if action_history_length > 0:
        history_len = action_history_length 
        # history_len = low_dim_obs_horizon-1
        arm_history = ArmActionHistory(max_length=history_len)
        print(f'history len: {history_len}')
        for _ in range(history_len):
            arm_history.add_action([0.0]*14)

    def _pred_func(self, views, actions) -> dict:
        left_agent_data = actions['follow1_pos'] # (7)
        right_agent_data = actions['follow2_pos'] # (7)
        if is_bi_mode:
            agent_data = np.concatenate([left_agent_data, right_agent_data])
        else:
            agent_data = np.array(right_agent_data)
        if history_len > 0:
            history_data = arm_history.get_history()
        if history_len > 0:
            agent_data = np.concatenate([history_data, agent_data.reshape(1,action_dim)], axis=0)
        agent_pos = np.array(agent_data).reshape(-1,action_dim)
        if use_gripper_cur:
            left_joint_cur = actions['follow1_joints_cur'][-1:] # (7)
            right_joint_cur = actions['follow2_joints_cur'][-1:] # (7)
            print(f"right_joint_cur: {right_joint_cur}")
            agent_pos = np.concatenate([agent_data, left_joint_cur, right_joint_cur], axis=-1).reshape(-1,action_dim+2)
        obs = {
            'face_view': views['camera_front'],
            'right_wrist_view': views['camera_right'],
            'left_wrist_view': views['camera_left'],
            'agent_pos': agent_pos,
        }

        with torch.no_grad():
            obs_dict = dict_apply(obs, 
                lambda x: torch.from_numpy(x).unsqueeze(0).to(device))
            batch = {
                'obs': obs_dict
            }
            # print(batch)
            instruction = args.instruction           
            if args.instruction is not None:
                batch['instruction'] = [instruction]
            if history_len > 0:
                batch['action_history'] = torch.from_numpy(np.array(arm_history.get_history())).unsqueeze(0).to(device) 
            result = policy.predict_action(batch)  
            action_pred = result['action_pred'][0].detach().to('cpu').numpy()
            [arm_history.add_action(action) for action in action_pred.tolist() if history_len > 0]
            left_action_pred = action_pred[:,:7]
            right_action_pred = action_pred[:,7:]

        actions_factor = 10
        # interpolates actions
        action_num = action_pred.shape[0]
        print(f'action_num: {action_num}')
        start_time = time.time()
        # left_action_pred = interpolates_actions(actions=action_pred[:,:7], num_actions=action_num, target_num_actions=actions_factor*action_num, action_dim=7)
        # right_action_pred = interpolates_actions(actions=action_pred[:,7:14], num_actions=action_num, target_num_actions=actions_factor*action_num, action_dim=7)
        
        end_time = time.time()
        # print("Time of interpolation: ", end_time - start_time)
        action_pred = np.concatenate([left_action_pred,right_action_pred], axis=1)
        # pass
        follow1 = action_pred[:, :7] # left EEF
        follow2 = action_pred[:, 7:14] # right EEF
        # follow1[:,-1] -= 0.1
        # follow1[:,-1] = np.maximum(follow1[:,-1], 0.0)
        follow2[:,-1] -= 0.3
        # follow2[:,-1] = np.maximum(follow2[:,-1], 0.0)
        head = [[0.0,-1.0] for _ in range(follow1.shape[0])]
        follow1 = follow1.tolist()
        follow2 = follow2.tolist()

        serialized_actions = {
            "follow1_pos":follow1,
            "follow2_pos":follow2,
            "follow1_joints":follow1,
            "follow2_joints":follow1
        }
        return serialized_actions
    
    RobotController.prediction = _pred_func
    robot_controller = RobotController(robot_id = args.robot_id, max_time_step=args.max_time_step)
    robot_controller.connect()
    print("robot connected")
    robot_controller.run(record_mode = args.record_mode)
    print("robot closing")
    robot_controller.close()


if __name__ == "__main__":
    # read args
    parser = argparse.ArgumentParser()
    parser.add_argument("--robot_id", type=int, default=0)
    parser.add_argument("--max_time_step", type=int, default=1000)
    parser.add_argument("--record_mode", action='store_true')

    parser.add_argument("--config_path", type=str, default=None)
    parser.add_argument("--checkpoint_path", type=str, default=None)
    parser.add_argument("--instructions", type=str, default=None)

    parser.add_argument("--use_ema", action='store_true')
    parser.add_argument("--action_start_ratio", type=float, default=0.25)
    parser.add_argument("--action_interpolate_multiplier", type=int, default=20)
    parser.add_argument("--instruction", type=str, default=None)

    args = parser.parse_args()

    #hang_clothes_0615_from_mix_right_mn
    # args.config_path = "/home/<USER>/projects/dp_ckpt/pickup_sponge/dp.yml"

    args.checkpoint_path ='/home/<USER>/projects/x2robot_infer/taikang/epoch=0080-train_loss=0.004.ckpt'
    # args.checkpoint_path ='/home/<USER>/projects/x2robot_infer/epoch=0065-train_loss=0.000.ckpt'
    # args.instructions = ["Pick up the sponge and place it on the plate"]

    # args.use_ema = True
    args.robot_id = 7
    main(args)
