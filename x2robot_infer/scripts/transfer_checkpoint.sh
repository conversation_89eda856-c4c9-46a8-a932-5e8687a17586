#!/bin/bash
# 定义变量
SOURCE_USER="jmwang"
SOURCE_HOST="**************"
SOURCE_PATH="/x2robot_v2/wjm/prj/diffusion_policy_logs/2025.07.01/03.09.26_resnet50_pick-up_waste-turtle/checkpoints/epoch=0025-train_loss=0.00397.ckpt"
LOCAL_PATH="/workspace"
# 显示开始信息
echo "开始从远程服务器拉取文件..."
echo "源: ${SOURCE_USER}@${SOURCE_HOST}:${SOURCE_PATH}"
echo "目标: ${LOCAL_PATH}"
# 确保目标目录存在
mkdir -p ${LOCAL_PATH}
# 执行SCP命令，从远程服务器拉取文件到本地
scp -C ${SOURCE_USER}@${SOURCE_HOST}:${SOURCE_PATH} ${LOCAL_PATH}
# 检查传输结果
if [ $? -eq 0 ]; then
    echo "文件传输成功!"
    echo "文件已保存到: ${LOCAL_PATH}$(basename ${SOURCE_PATH})"
else
    echo "文件传输失败，请检查错误信息。"
fi
