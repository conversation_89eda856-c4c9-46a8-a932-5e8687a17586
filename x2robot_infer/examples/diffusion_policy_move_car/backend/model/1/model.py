import cv2
import os
import sys
import json
import dill
import time
import torch
import hydra
import traceback
from PIL import Image

import triton_python_backend_utils as pb_utils
import numpy as np
from collections import deque
from itertools import islice
from scipy.spatial.transform import Rotation as R
from scipy.signal import savgol_filter
from diffusion_policy.policy.base_image_policy import BaseImagePolicy
from diffusion_policy.workspace.base_workspace import BaseWorkspace
from diffusion_policy.common.pytorch_util import dict_apply
from omegaconf import OmegaConf

# 注册OmegaConf解析器
OmegaConf.register_new_resolver("eval", eval, replace=True)

# ============================================================================
# 机器人物理限制和控制参数配置
# ============================================================================

# 高度限制
MIN_HEIGHT = 0.0  # m, 最小高度
MAX_HEIGHT = 0.4  # m, 最大高度

# 数值处理阈值
POSITION_ZERO_THRESHOLD = 0.001  # m, 位置零阈值

# 核心参数配置
# 机械臂关键帧选取
ARM_TRIM_START = 3
ARM_TRIM_END = 23  # 机械臂截取帧 3-23（20帧）

# 底盘关键帧选取
BASE_TRIM_START = 10
BASE_TRIM_END = 30  # 底盘截取帧 10-30（20帧）

INFERENCE_STEPS = 80

# 机械臂轨迹参数
ARM_MAX_VELOCITY = 0.02
ARM_EXECUTION_HZ = 20
ARM_MIN_EXECUTION_TIME = 5.0
ARM_MAX_EXECUTION_TIME = 15.0

class UnifiedTrajectoryProcessor:
    """统一轨迹处理器"""
    
    @staticmethod
    def interpolate_trajectory_batch(trajectories, target_length, smooth=True):
        """
        批量插值多个轨迹到统一长度
        Args:
            trajectories: list of np.array, 每个数组shape为(N, D)
            target_length: int, 目标长度
            smooth: bool, 是否平滑
        Returns:
            list of np.array, 插值后的轨迹
        """
        if not trajectories:
            return []
        
        results = []
        for traj in trajectories:
            if len(traj) == 0:
                results.append(np.zeros((target_length, traj.shape[1])))
                continue
                
            if len(traj) == target_length:
                results.append(traj)
                continue
                
            # 向量化插值
            original_indices = np.linspace(0, len(traj) - 1, len(traj))
            target_indices = np.linspace(0, len(traj) - 1, target_length)
            
            # 处理不同类型的数据
            if traj.shape[1] == 7:  # 机械臂数据 [x,y,z,rx,ry,rz,gripper]
                interpolated = UnifiedTrajectoryProcessor._interpolate_arm_trajectory(
                    traj, original_indices, target_indices, target_length
                )
            elif traj.shape[1] == 3:  # 位置数据 [x,y,theta]
                interpolated = UnifiedTrajectoryProcessor._interpolate_position_trajectory(
                    traj, original_indices, target_indices, target_length
                )
            else:  # 其他数据(高度、电流等)
                interpolated = np.zeros((target_length, traj.shape[1]))
                for i in range(traj.shape[1]):
                    interpolated[:, i] = np.interp(target_indices, original_indices, traj[:, i])
            
            # 平滑处理
            if smooth and len(interpolated) >= 5:
                interpolated = UnifiedTrajectoryProcessor._smooth_trajectory(interpolated)
            
            results.append(interpolated)
        
        return results
    
    @staticmethod
    def _interpolate_arm_trajectory(traj, original_indices, target_indices, target_length):
        """优化的机械臂轨迹插值"""
        interpolated = np.zeros((target_length, 7))
        
        # 向量化插值位置和夹爪
        for i in [0, 1, 2, 6]:  # x, y, z, gripper
            interpolated[:, i] = np.interp(target_indices, original_indices, traj[:, i])
        
        # 四元数插值(向量化)
        quaternions = R.from_euler('xyz', traj[:, 3:6]).as_quat()
        interpolated_quats = np.zeros((target_length, 4))
        for i in range(4):
            interpolated_quats[:, i] = np.interp(target_indices, original_indices, quaternions[:, i])
        
        # 批量归一化
        norms = np.linalg.norm(interpolated_quats, axis=1, keepdims=True)
        interpolated_quats = interpolated_quats / norms
        
        # 批量转换回欧拉角
        interpolated[:, 3:6] = R.from_quat(interpolated_quats).as_euler('xyz')
        
        return interpolated
    
    @staticmethod
    def _interpolate_position_trajectory(traj, original_indices, target_indices, target_length):
        """优化的位置轨迹插值"""
        interpolated = np.zeros((target_length, 3))
        for i in range(3):
            interpolated[:, i] = np.interp(target_indices, original_indices, traj[:, i])
        return interpolated
    
    @staticmethod
    def _smooth_trajectory(trajectory):
        """向量化平滑处理"""
        if len(trajectory) < 5:
            return trajectory
        
        try:
            # 批量平滑所有维度
            smoothed = np.zeros_like(trajectory)
            for dim in range(trajectory.shape[1]):
                smoothed[:, dim] = savgol_filter(
                    trajectory[:, dim], 
                    min(5, len(trajectory) if len(trajectory) % 2 == 1 else len(trajectory) - 1), 
                    3, 
                    mode='nearest'
                )
            return smoothed
        except:
            return trajectory
    
    @staticmethod
    def calculate_optimal_trajectory_length(left_traj, right_traj):
        """计算最优轨迹长度"""
        # 向量化距离计算
        def calc_distance(traj):
            if len(traj) < 2:
                return 0.0
            pos_diff = traj[1:, :3] - traj[:-1, :3]
            return np.sum(np.linalg.norm(pos_diff, axis=1))
        
        distances = [calc_distance(left_traj), calc_distance(right_traj)]
        max_distance = max(distances)
        
        if max_distance > 1e-6:
            execution_time = np.clip(
                max_distance / ARM_MAX_VELOCITY, 
                ARM_MIN_EXECUTION_TIME, 
                ARM_MAX_EXECUTION_TIME
            )
        else:
            execution_time = ARM_MIN_EXECUTION_TIME
        
        return max(int(execution_time * ARM_EXECUTION_HZ), len(left_traj))


class VehiclePoseHandler:
    """车辆位姿和速度计算"""
    
    def __init__(self):
        self.current_pose = None
        self.previous_pose = None
        self.pose_history = deque(maxlen=10)
    
    def update_pose(self, new_pose):
        """更新车辆位姿"""
        if new_pose is not None:
            self.previous_pose = self.current_pose
            self.current_pose = np.array(new_pose)
            self.pose_history.append(self.current_pose.copy())
        return self.current_pose
    
    def velocity_to_pose(self, vx_body, vy_body, vyaw, dt, start_pose=None):
        """将本体坐标系速度转换为全局坐标系位置"""
        if start_pose is None:
            if self.current_pose is not None:
                start_pose = self.current_pose.copy()
            else:
                start_pose = np.array([0.0, 0.0, 0.0])
        
        x, y, theta = start_pose
        
        # 本体坐标系速度转换为全局坐标系位移
        cos_theta = np.cos(theta)
        sin_theta = np.sin(theta)
        
        # 坐标变换：本体坐标系 -> 全局坐标系
        dx_global = (vx_body * cos_theta - vy_body * sin_theta) * dt
        dy_global = (vx_body * sin_theta + vy_body * cos_theta) * dt
        dtheta = vyaw * dt
        
        # 计算新位置
        x_new = x + dx_global
        y_new = y + dy_global
        theta_new = theta + dtheta
        
        # 将角度限制在[-pi, pi]范围内
        theta_new = (theta_new + np.pi) % (2 * np.pi) - np.pi
        
        return np.array([x_new, y_new, theta_new])
    
    def compute_body_velocities_from_poses(self, current_pose, previous_pose, dt=1/20):
        """从位姿变化计算本体坐标系速度"""
        if current_pose is None or previous_pose is None:
            return np.array([0.0, 0.0, 0.0])
        
        # 计算全局坐标系下的位移
        dx_global = current_pose[0] - previous_pose[0]
        dy_global = current_pose[1] - previous_pose[1]
        dtheta = current_pose[2] - previous_pose[2]
        
        # 使用前一帧的角度进行坐标变换
        theta = previous_pose[2]
        cos_theta = np.cos(theta)
        sin_theta = np.sin(theta)
        
        # 全局坐标系位移转换为本体坐标系速度
        vx_body = (dx_global * cos_theta + dy_global * sin_theta) / dt
        vy_body = (-dx_global * sin_theta + dy_global * cos_theta) / dt
        vyaw = dtheta / dt
        
        return np.array([vx_body, vy_body, vyaw])


        
class Visualizer:
    """图像处理和保存"""
    
    def __init__(self, save_path):
        self.save_path = save_path
        os.makedirs(self.save_path, exist_ok=True)
        self.combined_img_dir = os.path.join(self.save_path, "combined_views")
        os.makedirs(self.combined_img_dir, exist_ok=True)
    
    def save_combined_image(self, image1, image2, image3, count):
        """保存三个摄像头图像的组合视图"""
        img1 = Image.fromarray(image1, mode='RGB')
        img2 = Image.fromarray(image2, mode='RGB')
        img3 = Image.fromarray(image3, mode='RGB')
        
        gap = 10
        total_width = img1.width + img2.width + img3.width + gap * 2
        max_height = max(img1.height, img2.height, img3.height)
        combined_img = Image.new('RGB', (total_width, max_height), color=(255, 255, 255))
        
        combined_img.paste(img1, (0, 0))
        combined_img.paste(img2, (img1.width + gap, 0))
        combined_img.paste(img3, (img1.width + img2.width + gap * 2, 0))
        
        timestamp = time.strftime("%Y%m%d_%H%M%S_%f")[:-3]
        path = os.path.join(self.combined_img_dir, f"combined_{timestamp}_{count}.jpg")
        combined_img.save(path)
        
        return path
    
    def process_images_for_model(self, image1, image2, image3, img_obs_horizon):
        """处理图像为模型输入格式"""
        def _replicate_image(img, horizon):
            if img is None:
                return None
            replicated_img = np.tile(img[np.newaxis, ...], (horizon, 1, 1, 1))
            return replicated_img

        camera_left = _replicate_image(image1, img_obs_horizon)
        camera_front = _replicate_image(image2, img_obs_horizon)
        camera_right = _replicate_image(image3, img_obs_horizon)
        
        return camera_front, camera_left, camera_right


def read_img_triton(byte_data):
    """图像读取函数"""
    if byte_data is None or len(byte_data) == 0:
        return None
    
    nparr = np.frombuffer(byte_data, np.uint8)
    image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    if image is None:
        raise ValueError("Image decompression failed")
    
    return image


class TritonPythonModel:
    def initialize(self, args):
        self.logger = pb_utils.Logger
        self.device_kind = args["model_instance_kind"]  # 设备类型：CPU 或 GPU
        self.device_id = args["model_instance_device_id"]  # 设备ID（如 "0" 表示 GPU 0）

        # 根据设备类型设置 PyTorch 的 device
        if self.device_kind == "GPU":
            self.torch_device = f"cuda:{self.device_id}"
        else:
            self.torch_device = "cpu"

        self.model_config = model_config = json.loads(args["model_config"])

        # 从环境变量或配置文件中获取参数
        ckpt_path = os.environ.get("CKPT_PATH") or str(model_config["parameters"].get("ckpt_path", {}).get("string_value", ""))
        
        # 这些参数不再使用，因为已经优化为自适应轨迹长度计算

        self.instruction = os.environ.get("INSTRUCTION") or model_config["parameters"].get("instruction", {}).get("string_value", None)
        
        self.logger.log_info(f"checkpoint path: {ckpt_path}")
        
        # 加载模型
        payload = torch.load(open(ckpt_path, "rb"), pickle_module=dill)
        cfg = payload["cfg"]
        cls = hydra.utils.get_class(cfg._target_)

        # 解析观察键
        self.rgb_keys = list()
        self.lowdim_keys = list()
        obs_shape_meta = cfg.shape_meta.obs
        for key, attr in obs_shape_meta.items():
            type = attr.get("type", "low_dim")
            if type == "rgb":
                self.rgb_keys.append(key)
            elif type == "low_dim":
                self.lowdim_keys.append(key)

        # 设置工作空间 - 使用配置文件中的默认路径
        workspace = cls(cfg, cfg.multi_run.run_dir)
        workspace: BaseWorkspace
        workspace.load_payload(payload, exclude_keys=None, include_keys=None)
        
        # 获取策略模型
        self.policy: BaseImagePolicy = workspace.model
        self.policy.eval().to(self.torch_device)
        self.policy.num_inference_steps = INFERENCE_STEPS
        
        # 模型配置
        self.img_obs_horizon = cfg.task.img_obs_horizon
        self.is_bi_mode = cfg.task.dataset.is_bi_mode
        self.use_gripper_cur = cfg.task.use_gripper_cur if hasattr(cfg.task, "use_gripper_cur") else False     
        
        # 检查是否有环境变量覆盖电流数据使用
        env_use_gripper_cur = os.environ.get("USE_GRIPPER_CUR")
        if env_use_gripper_cur is not None:
            self.use_gripper_cur = env_use_gripper_cur.lower() in ['true', '1', 'yes', 'on']
        
        # 初始化车辆位姿处理器
        self.vehicle_pose_handler = VehiclePoseHandler()
        
        # 运行时状态
        self.predicted_base_velocity = None
        
        # 图像保存相关初始化 - 已注释
        # self.image_save_dir = "/workspace/images"
        # self.image_counter = 0
        # self.visualizer = Visualizer(self.image_save_dir)
        
        self.logger.log_info("Model initialization completed")

    def process_images_for_model(self, image1, image2, image3, img_obs_horizon):
        """处理图像为模型输入格式"""
        def _replicate_image(img, horizon):
            if img is None:
                return None
            replicated_img = np.tile(img[np.newaxis, ...], (horizon, 1, 1, 1))
            return replicated_img

        camera_left = _replicate_image(image1, img_obs_horizon)
        camera_front = _replicate_image(image2, img_obs_horizon)
        camera_right = _replicate_image(image3, img_obs_horizon)
        
        return camera_front, camera_left, camera_right

    def _pred_func(self, views, inputs) -> dict:
        # 1. 处理输入数据
        left_agent_data = inputs["follow1_pos"]
        right_agent_data = inputs["follow2_pos"]
        
        # 处理车辆位姿数据
        car_pose_data = inputs.get("car_pose", None)
        if car_pose_data is not None:
            self.vehicle_pose_handler.update_pose(car_pose_data)
        
        # 检查高度数据
        height_data = inputs.get("lift", None)
        if height_data is not None:
            height_data = [height_data[0]] if isinstance(height_data, (list, np.ndarray)) else [height_data]
        
        # 处理头部位置数据
        head_pos_data = inputs.get("head_pos", None)
        if head_pos_data is not None:
            # 使用实际接收到的头部位置数据
            if isinstance(head_pos_data, (list, np.ndarray)):
                head_current_state = np.array(head_pos_data[:2])  # 取前两个值
            else:
                head_current_state = np.array([head_pos_data, 0.0])  # 如果只有一个值，补充第二个值
        else:
            # 如果没有接收到头部数据，使用默认值
            head_current_state = np.array([0.0, -1.0])
        
        # 计算本体坐标系速度：使用预测速度保持连续性，初始状态为零
        if self.predicted_base_velocity is not None:
            # 使用上一次预测的第20帧速度
            base_velocity = self.predicted_base_velocity.copy()
            self.logger.log_info(f"Using predicted base_velocity as observation: {base_velocity}")
        else:
            # 初始状态：零速度
            base_velocity = np.array([0.0, 0.0, 0.0])
            self.logger.log_info("Using initial zero base_velocity as observation")
        
        # 处理关节电流数据
        left_joint_cur = None
        right_joint_cur = None
        
        if self.use_gripper_cur:
            # 从7维电流数据中提取最后一个值（通常是夹爪电流）
            left_joint_cur_raw = inputs["follow1_joints_cur"]
            right_joint_cur_raw = inputs["follow2_joints_cur"]
            
            if len(left_joint_cur_raw) >= 1:
                left_joint_cur = np.array([left_joint_cur_raw[-1]])  # 取最后一个值
            else:
                left_joint_cur = np.array([0.0])
                
            if len(right_joint_cur_raw) >= 1:
                right_joint_cur = np.array([right_joint_cur_raw[-1]])  # 取最后一个值
            else:
                right_joint_cur = np.array([0.0])
        else:
            # 如果不使用电流数据，提供默认的零电流值以保证22维输入
            left_joint_cur = np.array([0.0])
            right_joint_cur = np.array([0.0])
        
        # 构建agent_data
        if self.is_bi_mode:
            agent_data = np.concatenate([left_agent_data, right_agent_data], axis=-1, dtype=np.float32)
        else:
            agent_data = np.array(right_agent_data)
        
        # 构建22维输入数据
        height_data = [0.0] if height_data is None else height_data
        move_data = np.concatenate([base_velocity, height_data], axis=0)
        
        agent_data = np.concatenate([agent_data, move_data, head_current_state], axis=0)
        
        # 始终添加电流数据（如果启用则使用实际值，否则使用零值）
        joint_cur_data = np.concatenate([left_joint_cur, right_joint_cur], axis=0)
        
        agent_data = np.concatenate([agent_data, joint_cur_data], axis=0)
        
        # 直接构建22维agent_pos
        agent_pos = np.array(agent_data).reshape(-1, 22)
        
        # 2. 准备观测数据
        obs = {
            'face_view': views["camera_front"],
            'left_wrist_view': views["camera_left"],
            'right_wrist_view': views["camera_right"],
            'agent_pos': agent_pos,
        }
        
        # 3. 运行模型推理
        with torch.no_grad():
            obs_dict = dict_apply(obs, lambda x: torch.from_numpy(x).unsqueeze(0).to(self.torch_device))
            batch = {'obs': obs_dict}
            
            # 添加指令（如果有）
            if self.instruction is not None:
                batch['instruction'] = [self.instruction]
            
            # 运行推理
            result = self.policy.predict_action(batch)
            action_pred = result['action_pred'][0].detach().to('cpu').numpy()
                
        # 5. 使用统一轨迹处理器优化插值流程
        # 分别截取机械臂和底盘的关键帧段
        max_required_frame = max(ARM_TRIM_END, BASE_TRIM_END)
        
        if len(action_pred) < max_required_frame:
            self.logger.log_info(f"Prediction length {len(action_pred)} insufficient for max frame {max_required_frame}, using original sequence")
            arm_action_pred = action_pred.copy()
            move_action_pred = action_pred.copy()
        else:
            # 机械臂选取关键帧 3-23（20帧）
            arm_action_pred = action_pred[ARM_TRIM_START:ARM_TRIM_END]
            # 底盘选取关键帧 10-30（20帧）
            move_action_pred = action_pred[BASE_TRIM_START:BASE_TRIM_END]
            
            self.logger.log_info(f"ARM: using frames {ARM_TRIM_START}-{ARM_TRIM_END} ({len(arm_action_pred)} frames)")
            self.logger.log_info(f"BASE: using frames {BASE_TRIM_START}-{BASE_TRIM_END} ({len(move_action_pred)} frames)")
        
        # 提取左右臂轨迹
        left_traj_init = arm_action_pred[:,:7]
        right_traj_init = arm_action_pred[:,7:14]
        
        # 计算最优轨迹长度
        optimal_length = UnifiedTrajectoryProcessor.calculate_optimal_trajectory_length(
            left_traj_init, right_traj_init
        )
        
        # 批量插值和平滑处理
        trajectories = [left_traj_init, right_traj_init]
        left_action_pred, right_action_pred = UnifiedTrajectoryProcessor.interpolate_trajectory_batch(
            trajectories, optimal_length, smooth=True
        )
        
        self.logger.log_info(f"Unified trajectory processing: {len(left_traj_init)} -> {optimal_length} points")
        
        # 6. 处理22维模型的移动数据
        # 提取速度预测
        base_velocity_pred = move_action_pred[:, 14:17]
        
        # 缓存底盘最后一帧（第20帧）预测速度用于下次预测的连续性
        if len(base_velocity_pred) > 0:
            self.predicted_base_velocity = base_velocity_pred[-1].copy()
            self.logger.log_info(f"Cached frame-20 base_velocity: {self.predicted_base_velocity}")
        
        # 向量化积分速度为位置
        dt = 1/20
        current_pose = self.vehicle_pose_handler.current_pose.copy() if self.vehicle_pose_handler.current_pose is not None else np.array([0.0, 0.0, 0.0])
        
        # 批量积分计算位置
        poses_20_frames = []
        for i in range(len(base_velocity_pred)):
            current_pose = self.vehicle_pose_handler.velocity_to_pose(
                base_velocity_pred[i, 0], base_velocity_pred[i, 1], base_velocity_pred[i, 2], 
                dt, current_pose
            )
            poses_20_frames.append(current_pose.copy())
        
        # 提取高度预测
        height_pred = move_action_pred[:, 17:18]
        
        # 提取头部预测
        head_pred = move_action_pred[:, 18:20]
        
        # 批量插值处理
        poses_20_frames = np.array(poses_20_frames)
        trajectories_to_interpolate = [poses_20_frames, height_pred, head_pred]
        
        target_length = left_action_pred.shape[0]
        interpolated_results = UnifiedTrajectoryProcessor.interpolate_trajectory_batch(
            trajectories_to_interpolate, target_length, smooth=True
        )
        
        position_interp = interpolated_results[0]
        height_interp = interpolated_results[1]
        head_interp = interpolated_results[2]
        
        self.logger.log_info("22-dim model: using predicted head values")
        
        # 7. 生成最终输出
        follow1 = left_action_pred.copy()
        follow2 = right_action_pred.copy()
        
        # 调整夹爪值：减少0.5
        follow1[:, 6] = follow1[:, 6] - 0.01  # 左臂夹爪值减少0.01
        follow2[:, 6] = follow2[:, 6] - 0.01  # 右臂夹爪值减少0.01
        self.logger.log_info(f"Adjusted gripper values: left range [{np.min(follow1[:, 6]):.3f}, {np.max(follow1[:, 6]):.3f}], right range [{np.min(follow2[:, 6]):.3f}, {np.max(follow2[:, 6]):.3f}]")
        
        # 生成头部动作（使用模型预测值）
        head = head_interp.tolist()
        self.logger.log_info(f"Head action: using predicted values for {follow1.shape[0]} frames")
        self.logger.log_info(f"Head action range: [{np.min(head_interp):.3f}, {np.max(head_interp):.3f}]")
        
        # 创建响应数据
        data_dir = self._create_response_data(
            follow1, follow2, head, 
            height_interp, position_interp
        )
        
        return data_dir
    
    def _create_response_data(self, follow1, follow2, head, height_interp, position_interp):
        """创建响应数据"""
        # 基本响应数据
        follow1_list = follow1.tolist() if isinstance(follow1, np.ndarray) else follow1
        follow2_list = follow2.tolist() if isinstance(follow2, np.ndarray) else follow2
        
        data_dir = {
            "follow1_pos": follow1_list,
            "follow2_pos": follow2_list,
            "follow1_joints": follow1_list,
            "follow2_joints": follow2_list,
            "head_pos": head
        }
        
        # 移动操作处理 - 位置控制
        car_poses = []
        for i in range(len(position_interp)):
            pose = position_interp[i]
            
            # 位置极小值过滤
            pose_clean = pose.copy()
            pose_clean[np.abs(pose_clean) < POSITION_ZERO_THRESHOLD] = 0.0
            
            car_poses.append([round(val, 2) for val in pose_clean])
        
        data_dir["car_pose"] = car_poses
        
        # 高度数据处理
        height_list = []
        for h in height_interp.tolist():
            h_val = h[0] if isinstance(h, list) else h
            h_val = np.clip(h_val, MIN_HEIGHT, MAX_HEIGHT)
            if abs(h_val) < POSITION_ZERO_THRESHOLD: h_val = 0.0
            if h_val <= 0.05: h_val = 0.0
            height_list.append(round(h_val, 2))
        data_dir["lift"] = height_list
        
        self.logger.log_info(f"Position control: sending {len(car_poses)} global position points")
        
        # 电流数据输出（默认值）
        default_length = len(follow1_list)
        data_dir["follow1_joints_cur"] = [[0.0] for _ in range(default_length)]
        data_dir["follow2_joints_cur"] = [[0.0] for _ in range(default_length)]
        
        return data_dir

    def execute(self, requests):
        """处理推理请求，生成机械臂控制指令"""
        responses = []
        start_time = time.time()
        for request in requests:
            try:
                # 1. 从请求中提取必需的输入张量
                inputs = {
                    "follow1_pos": pb_utils.get_input_tensor_by_name(request, "ACTION_FOLLOW1_POS").as_numpy(),
                    "follow2_pos": pb_utils.get_input_tensor_by_name(request, "ACTION_FOLLOW2_POS").as_numpy(),
                    "follow1_joints_cur": pb_utils.get_input_tensor_by_name(request, "ACTION_FOLLOW1_JOINTS_CUR").as_numpy(),
                    "follow2_joints_cur": pb_utils.get_input_tensor_by_name(request, "ACTION_FOLLOW2_JOINTS_CUR").as_numpy(),
                }

                # 设置指令
                inputs["instruction"] = self.instruction
                
                # 提取可选输入
                car_pose_tensor = pb_utils.get_input_tensor_by_name(request, "CAR_POSE")
                if car_pose_tensor:
                    inputs["car_pose"] = car_pose_tensor.as_numpy()
                
                lift_tensor = pb_utils.get_input_tensor_by_name(request, "LIFT")
                if lift_tensor:
                    inputs["lift"] = lift_tensor.as_numpy()
                
                head_pos_tensor = pb_utils.get_input_tensor_by_name(request, "HEAD_POS")
                if head_pos_tensor:
                    inputs["head_pos"] = head_pos_tensor.as_numpy()
                
                # 处理可选摄像头输入
                views = {}
                raw_images = {}  # 保存原始图像用于拼接
                
                for cam_name in ["CAMERA_LEFT", "CAMERA_FRONT", "CAMERA_RIGHT"]:
                    tensor = pb_utils.get_input_tensor_by_name(request, cam_name)
                    if not tensor:
                        continue  # 跳过不存在的可选输入
                        
                    # 获取字节流数据（形状为[1]的BYTES类型）
                    byte_data_array = tensor.as_numpy()
                    if byte_data_array.size == 0:
                        continue
                        
                    # 解压第一个元素（因为dims=[1]）
                    byte_data = byte_data_array[0]
                    try:
                        img = read_img_triton(byte_data)
                        if img is not None:
                            raw_images[cam_name] = img
                    except Exception as e:
                        self.logger.log_error(f"Failed to process {cam_name} image: {str(e)}")

                # 处理图像数据
                if raw_images:
                    self.logger.log_info(f"Acquired view images: {list(raw_images.keys())}")
                    
                    left_img = raw_images.get("CAMERA_LEFT", None)
                    front_img = raw_images.get("CAMERA_FRONT", None)
                    right_img = raw_images.get("CAMERA_RIGHT", None)
                    
                    # 保存三视角拼接图像 - 已注释
                    # self.image_counter += 1
                    # if left_img is not None and front_img is not None and right_img is not None:
                    #     saved_path = self.visualizer.save_combined_image(left_img, front_img, right_img, self.image_counter)
                    # else:
                    #     self.logger.log_info("Some images are missing, skipping save")
                    
                    # 处理图像为模型输入格式
                    if left_img is not None and front_img is not None and right_img is not None:
                        camera_front, camera_left, camera_right = self.process_images_for_model(
                            left_img, front_img, right_img, self.img_obs_horizon
                        )
                        views = {
                            "camera_left": camera_left,
                            "camera_front": camera_front,
                            "camera_right": camera_right,
                        }
                else:
                    self.logger.log_info("No view images acquired, using default images")

                # 如果没有图像输入，创建默认的零图像
                if not views:
                    default_shape = (self.img_obs_horizon, 240, 320, 3)
                    default_image = np.zeros(default_shape, dtype=np.uint8)
                    views = {
                        "camera_left": default_image,
                        "camera_front": default_image,
                        "camera_right": default_image,
                    }
                    self.logger.log_info("Using default zero images")

                # 3. 执行预测
                with torch.no_grad():
                    result_data = self._pred_func(views, inputs)

                # 4. 构建输出张量
                output_tensors = []
                
                # 添加机械臂输出
                arm_outputs = ["FOLLOW1_POS", "FOLLOW2_POS", "FOLLOW1_JOINTS", "FOLLOW2_JOINTS"]
                for output_name in arm_outputs:
                    key = output_name.lower()
                    data = np.array(result_data[key], dtype=np.float32)
                    output_tensors.append(pb_utils.Tensor(output_name, data))
                
                # 添加头部输出
                head_data = np.array(result_data["head_pos"], dtype=np.float32)
                output_tensors.append(pb_utils.Tensor("HEAD_POS", head_data))
                
                # 添加高度输出
                lift_data = np.array(result_data["lift"], dtype=np.float32)
                if lift_data.ndim == 1:
                    lift_data = lift_data.reshape(-1, 1)
                output_tensors.append(pb_utils.Tensor("LIFT_OUT", lift_data))
                
                # 添加电流输出
                for joint_name, tensor_name in [("follow1_joints_cur", "FOLLOW1_JOINTS_CUR_OUT"), 
                                               ("follow2_joints_cur", "FOLLOW2_JOINTS_CUR_OUT")]:
                    if joint_name in result_data:
                        joint_data = np.array(result_data[joint_name], dtype=np.float32)
                        output_tensors.append(pb_utils.Tensor(tensor_name, joint_data))
                
                # 添加位置控制输出
                if "car_pose" in result_data:
                    car_pose_data = np.array(result_data["car_pose"], dtype=np.float32)
                    output_tensors.append(pb_utils.Tensor("CAR_POSE_OUT", car_pose_data))

                # 5. 构造响应
                responses.append(pb_utils.InferenceResponse(output_tensors))

            except Exception as e:
                # 获取完整错误堆栈
                exc_type, exc_value, exc_tb = sys.exc_info()
                tb_lines = traceback.format_exception(exc_type, exc_value, exc_tb)
                full_traceback = "".join(tb_lines)
                relevant_stack = "\n".join([line.strip() for line in islice(full_traceback.split("\n"), 0, 15)])

                # 记录完整堆栈到服务端日志
                self.logger.log_error(f"Full error traceback:\n{full_traceback}")

                # 返回精简的客户端错误信息
                error_msg = f"Inference failed: {str(e)}\nRecent call stack:\n{str(relevant_stack)}"
                error = pb_utils.TritonError(error_msg)
                responses.append(pb_utils.InferenceResponse(error=error))
                
        output_duration = time.time() - start_time
        self.logger.log_info(f"Processed {len(requests)} requests in {output_duration:.2f} seconds")
        return responses
