import os
os.environ["CUDA_VISIBLE_DEVICES"] = "0,1"
device='cuda:1'
import torch
import argparse
import numpy as np
import cv2
import re

from eval.utils import interpolates_actions, interpolate_actions_with_hand
from eval.robot_controller import RobotController
import hydra
import dill
from diffusion_policy.policy.base_image_policy import BaseImagePolicy
from diffusion_policy.workspace.base_workspace import BaseWorkspace
from utils import ArmActionHistory
from omegaconf import OmegaConf
from models.high_low_policy_manager import HighPolicyManager

# A comprehensive mapping from model's semantic keys to robot controller's keys and their properties.
MODEL_TO_ROBOT_MAPPING = {
    # Arm cartesian pose (7D: 3 pos, 3 rot, 1 gripper)
    'master_left_ee_cartesian_pos':  {'name': 'follow1_pos', 'shape': 3, 'slice': slice(0, 3)},
    'follow_left_ee_cartesian_pos':  {'name': 'follow1_pos', 'shape': 3, 'slice': slice(0, 3)},
    'master_left_ee_rotation':       {'name': 'follow1_pos', 'shape': 3, 'slice': slice(3, 6)},
    'follow_left_ee_rotation':       {'name': 'follow1_pos', 'shape': 3, 'slice': slice(3, 6)},
    'master_left_gripper':           {'name': 'follow1_pos', 'shape': 1, 'slice': slice(6, 7)},
    'follow_left_gripper':           {'name': 'follow1_pos', 'shape': 1, 'slice': slice(6, 7)},
    'master_right_ee_cartesian_pos': {'name': 'follow2_pos', 'shape': 3, 'slice': slice(0, 3)},
    'follow_right_ee_cartesian_pos': {'name': 'follow2_pos', 'shape': 3, 'slice': slice(0, 3)},
    'master_right_ee_rotation':      {'name': 'follow2_pos', 'shape': 3, 'slice': slice(3, 6)},
    'follow_right_ee_rotation':      {'name': 'follow2_pos', 'shape': 3, 'slice': slice(3, 6)},
    'master_right_gripper':          {'name': 'follow2_pos', 'shape': 1, 'slice': slice(6, 7)},
    'follow_right_gripper':          {'name': 'follow2_pos', 'shape': 1, 'slice': slice(6, 7)},
    
    # Gripper current (from robot's followX_joints_cur)
    'follow_left_gripper_cur':       {'name': 'follow1_joints_cur', 'shape': 1, 'slice': slice(-1, None)},
    'follow_right_gripper_cur':      {'name': 'follow2_joints_cur', 'shape': 1, 'slice': slice(-1, None)},

    # Other modalities
    'velocity_decomposed':           {'name': 'car_pose', 'shape': 3},
    'height':                        {'name': 'lift', 'shape': 1},
    'head_rotation':                 {'name': 'head_pos', 'shape': 2},
}

def velocity_to_pose(vx_body, vy_body, vyaw, dt, start_pose):
    """Converts body frame velocity to global frame pose."""
    x, y, theta = start_pose
    cos_theta, sin_theta = np.cos(theta), np.sin(theta)
    dx_global = (vx_body * cos_theta - vy_body * sin_theta) * dt
    dy_global = (vx_body * sin_theta + vy_body * cos_theta) * dt
    dtheta = vyaw * dt
    x_new, y_new = x + dx_global, y + dy_global
    theta_new = (theta + dtheta + np.pi) % (2 * np.pi) - np.pi
    return np.array([x_new, y_new, theta_new])

def build_action_masks(pred_keys, mapping):
    """Builds boolean masks for slicing the flat action tensor."""
    total_dim = sum(mapping.get(k, {'shape': 0})['shape'] for k in pred_keys)
    masks, cursor = {}, 0
    for k in pred_keys:
        if k not in mapping: continue
        dim = mapping[k]['shape']
        m = np.zeros(total_dim, dtype=bool)
        m[cursor:cursor + dim] = True
        masks[k] = m
        cursor += dim
    return masks

def draw_point_on_image(image, instruction, save_path):
    match = re.search(r'\((\d+),\s*(\d+)\)', instruction)
    if not match: return
    
    x, y = int(match.group(1)), int(match.group(2))
    img_bgr = cv2.cvtColor(image.copy(), cv2.COLOR_RGB2BGR)
    
    if img_bgr.dtype != np.uint8:
        img_bgr = (img_bgr * 255).astype(np.uint8) if img_bgr.max() <= 1.0 else img_bgr.astype(np.uint8)
        
    cv2.circle(img_bgr, (x, y), radius=5, color=(0, 0, 255), thickness=-1)
    
    if not cv2.imwrite(save_path, img_bgr):
        print(f"Error: Failed to save debug image to {save_path}.", flush=True)

def main(args):
    # Load model and configuration
    print(f'Loading checkpoint: {args.checkpoint_path}')
    payload = torch.load(open(args.checkpoint_path, 'rb'), pickle_module=dill)
    cfg = payload['cfg']
    cls = hydra.utils.get_class(cfg._target_)

    # Dynamically load keys from config
    obs_action_keys = list(cfg.task.obs_action_keys)
    predict_action_keys = list(cfg.task.predict_action_keys)
    print(f"Loaded obs_action_keys: {obs_action_keys}")
    print(f"Loaded predict_action_keys: {predict_action_keys}")
    predict_action_masks = build_action_masks(predict_action_keys, MODEL_TO_ROBOT_MAPPING)

    # Setup policy
    workspace = cls(cfg)
    workspace.load_payload(payload)
    policy: BaseImagePolicy = workspace.model
    policy.eval().to(device)
    policy.num_inference_steps = 100

    # Setup HighPolicyManager if provided
    high_policy_manager = None
    if args.high_policy_model_path:
        high_policy_manager = HighPolicyManager(
            model_path=args.high_policy_model_path,
            update_interval=args.high_policy_update_interval
        )
        print(f'HighPolicyManager initialized with model: {args.high_policy_model_path}')

    # Get observation shape info
    rgb_keys, lowdim_keys = [], []
    for key, attr in cfg.shape_meta.obs.items():
        if attr.get('type') == 'rgb': rgb_keys.append(key)
        elif attr.get('type') == 'low_dim': lowdim_keys.append(key)
    obs_action_dim = sum(MODEL_TO_ROBOT_MAPPING[key]['shape'] for key in obs_action_keys)

    def _pred_func(self, views, actions) -> dict:
        if not hasattr(self, 'cur_velocity_decomposed'):
            self.cur_velocity_decomposed = np.array([0.0, 0.0, 0.0])
        current_instruction = args.task
        # Update instruction from High Policy Manager
        if high_policy_manager:
            try:
                instruction, _ = high_policy_manager.update_instruction(
                    views['camera_front'], views['camera_left'], views['camera_right'], args.task
                )
                current_instruction = instruction or high_policy_manager.get_current_instruction()
                print(f"High-Policy Instruction: {current_instruction}", flush=True)
                draw_point_on_image(views['camera_front'], current_instruction, '/tmp/face_view_with_dot.jpg')
            except Exception as e:
                print(f"Error in HighPolicyManager: {e}", flush=True)

        # Build agent_pos from robot actions based on config
        obs_list = []
        for key in obs_action_keys:
            mapping_info = MODEL_TO_ROBOT_MAPPING[key]
            robot_key, data_slice = mapping_info['name'], mapping_info.get('slice')
            if robot_key not in actions:
                raise ValueError(f"Required robot key '{robot_key}' for model key '{key}' not in received actions")
            if key == 'velocity_decomposed':
                obs_list.append(self.cur_velocity_decomposed)
            else:
                raw_data = actions[robot_key]
                if isinstance(raw_data, float) or isinstance(raw_data, int):
                    raw_data = np.array([raw_data])
                obs_list.append(np.array(raw_data[data_slice] if data_slice else raw_data))
            print(f'{key}: {obs_list[-1].shape}')
        
        agent_pos = np.concatenate(obs_list).reshape(1, -1)
        if agent_pos.shape[-1] != obs_action_dim:
            raise ValueError(f'agent_pos dim mismatch! Got {agent_pos.shape[-1]}, expected {obs_action_dim}')

        # Prepare batch for model
        obs_dict = {'agent_pos': agent_pos}
        
        # Explicitly map model's RGB keys to the robot's camera keys
        view_mapping = {
            'face_view': 'camera_front',
            'left_wrist_view': 'camera_left',
            'right_wrist_view': 'camera_right',
        }

        for key in rgb_keys:
            if key in view_mapping:
                robot_view_key = view_mapping[key]
                if robot_view_key in views:
                    obs_dict[key] = views[robot_view_key]
                else:
                    print(f"Warning: Robot camera key '{robot_view_key}' not found in views dict for model key '{key}'.")
            else:
                print(f"Warning: No mapping found for model RGB key '{key}'. It will be missing from the observation.")
        
        batch = {'obs': {k: torch.from_numpy(v).unsqueeze(0).to(device) for k, v in obs_dict.items() if k in rgb_keys or k in lowdim_keys}}
        if high_policy_manager:
            batch['instruction'] = [current_instruction]
        
        # Run inference
        with torch.no_grad():
            result = policy.predict_action(batch)
            action_pred = result['action_pred'][0].cpu().numpy()

        # Parse predicted action tensor
        parsed_actions = {key: action_pred[:, predict_action_masks[key]] for key in predict_action_keys}
        num_steps = action_pred.shape[0]

        # Process predicted actions
        current_car_pose = np.array(actions.get('car_pose', [0,0,0]))
        target_car_poses = np.tile(current_car_pose, (num_steps, 1))
        
        if 'velocity_decomposed' in parsed_actions:
            velocities = parsed_actions['velocity_decomposed']
            temp_poses = []
            for i in range(num_steps):
                current_car_pose = velocity_to_pose(velocities[i,0], velocities[i,1], velocities[i,2], 0.05, current_car_pose)
                temp_poses.append(current_car_pose)
            target_car_poses = np.array(temp_poses)
            self.cur_velocity_decomposed = velocities[-1]
        
        def assemble_arm_action(side):
            """
            Assembles the full arm action by dynamically checking for predicted 
            master or follow keys.
            """
            # Dynamically determine which position and rotation keys to use based on what the model predicted.
            pos_key = f'master_{side}_ee_cartesian_pos'
            if pos_key not in parsed_actions:
                pos_key = f'follow_{side}_ee_cartesian_pos'

            rot_key = f'master_{side}_ee_rotation'
            if rot_key not in parsed_actions:
                rot_key = f'follow_{side}_ee_rotation'
            
            # Gripper key is consistently 'follow' in most configs.
            grip_key = f'follow_{side}_gripper'
            if grip_key not in parsed_actions:
                grip_key = f'master_{side}_gripper'
            
            # Collect the predicted parts that are available in parsed_actions.
            # The order of keys here is important to form the 7D action vector correctly.
            parts = []
            for key in [pos_key, rot_key, grip_key]:
                if key in parsed_actions:
                    parts.append(parsed_actions[key])
            
            return np.concatenate(parts, axis=1) if parts else None

        follow1 = assemble_arm_action('left')
        follow2 = assemble_arm_action('right')
        lift = parsed_actions.get('height')
        head_rotation = parsed_actions.get('head_rotation')

        # Interpolate and slice
        inter_len = args.action_interpolate_multiplier * num_steps
        start_frame, end_frame = int(args.action_start_ratio * inter_len), int(args.action_end_ratio * inter_len)
        
        def interpolate_arm(data, default_val):
            if data is None:
                return [default_val] * (end_frame - start_frame)
            # Use the specialized interpolation function for 7D arm actions
            interp = interpolates_actions(data, num_steps, inter_len, data.shape[1])
            return interp[start_frame:end_frame].tolist()
            
        def interpolate_simple(data, default_val):
            if data is None:
                return [default_val] * (end_frame - start_frame)
            # Use simple linear interpolation for other values (car, lift, head)
            original_indices = np.linspace(0, 1, num_steps)
            target_indices = np.linspace(0, 1, inter_len)
            interp_data = np.zeros((inter_len, data.shape[1]))
            for i in range(data.shape[1]):
                interp_data[:, i] = np.interp(target_indices, original_indices, data[:, i])
            return interp_data[start_frame:end_frame].tolist()
        print(f'lift:{lift.shape}')
        #import pdb; pdb.set_trace()

        return {
            "follow1_pos": interpolate_arm(follow1, actions.get('follow1_pos')),
            "follow2_pos": interpolate_arm(follow2, actions.get('follow2_pos')),
            "car_pose": interpolate_simple(target_car_poses, actions.get('car_pose')),
            #"lift": interpolate_simple([lift], [actions.get('lift', 0.4)]),
            "lift": [e[0] for e in interpolate_simple(lift, [actions.get('lift', 0.4)])],
            "head_pos": interpolate_simple(head_rotation, actions.get('head_pos')),
            "follow1_joints": [], "follow2_joints": [], # Not handled
        }

    RobotController.prediction = _pred_func
    robot_controller = RobotController(robot_id=args.robot_id, max_time_step=args.max_time_step)
    robot_controller.connect()
    print("Robot connected.")
    try:
        robot_controller.run(record_mode=args.record_mode)
    finally:
        print("Robot closing.")
        robot_controller.close()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run Diffusion Policy Inference on Robot")
    parser.add_argument("--checkpoint_path", type=str, help="Path to the model checkpoint file.")
    parser.add_argument("--robot_id", type=int, default=10001, help="Robot ID (10001 for local server).")
    parser.add_argument("--max_time_step", type=int, default=1000000)
    parser.add_argument("--record_mode", action='store_true')
    parser.add_argument("--action_start_ratio", type=float, default=0.0)
    parser.add_argument("--action_end_ratio", type=float, default=1.0)
    parser.add_argument("--action_interpolate_multiplier", type=int, default=10)
    parser.add_argument("--head_pos", type=float, nargs=2, default=[0.0, -1.0])
    
    # High-level policy arguments
    parser.add_argument("--high_policy_model_path", type=str, help="Path to the high policy model (e.g., Qwen).", default=None)
    parser.add_argument("--high_policy_update_interval", type=float, default=5.0, help="Interval to update instruction from high policy.")
    parser.add_argument("--task", type=str, default="Default task instruction.", help="Default task description.")
    
    args = parser.parse_args()
    

    # 20250704
    args.checkpoint_path = '/x2robot_v2/ganruyi/workspace/diffusion_policy/2025.06.28/03.12.50_pretrain_sort_and_fold_sort_and_fold/checkpoints/epoch=0065-train_loss=0.005.ckpt' # sort and fold, have problem, no move
    args.checkpoint_path = '/x2robot_v2/ganruyi/workspace/diffusion_policy/2025.06.28/03.12.50_pretrain_sort_and_fold_sort_and_fold/checkpoints/epoch=0020-train_loss=0.008.ckpt'
    # args.checkpoint_path  = '/x2robot_v2/ganruyi/workspace/diffusion_policy/2025.06.24/14.44.03_pretrain_sort_and_fold_sort_and_fold/checkpoints/epoch=0010-train_loss=0.006.ckpt' # have problem, no move
    # args.checkpoint_path = '/x2robot_v2/ganruyi/workspace/diffusion_policy/2025.06.28/03.36.28_fold_towel_takeover_fold_towel_takeover/checkpoints/epoch=0080-train_loss=0.001.ckpt' # take over
    
    args.checkpoint_path = '/x2robot_v2/ganruyi/workspace/diffusion_policy/2025.07.02/14.09.39_tidy_clothes_tidy_clothes/checkpoints/epoch=0055-train_loss=0.001.ckpt' # move clothes
    args.robot_id = 10001
    main(args)  
