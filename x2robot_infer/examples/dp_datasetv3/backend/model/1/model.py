import cv2
import os
import sys
import json
import dill
import time
import torch
import hydra
import traceback
from PIL import Image

import triton_python_backend_utils as pb_utils
import numpy as np
from collections import deque
from itertools import islice
from scipy.spatial.transform import Rotation as R
from scipy.signal import savgol_filter
from diffusion_policy.policy.base_image_policy import BaseImagePolicy
from diffusion_policy.workspace.base_workspace import BaseWorkspace
from diffusion_policy.common.pytorch_util import dict_apply
from omegaconf import OmegaConf

# 注册OmegaConf解析器
OmegaConf.register_new_resolver("eval", eval, replace=True)

# A comprehensive mapping from model's semantic keys to robot controller's keys and their properties.
MODEL_TO_ROBOT_MAPPING = {
    # Arm cartesian pose (7D: 3 pos, 3 rot, 1 gripper)
    'master_left_ee_cartesian_pos':  {'name': 'follow1_pos', 'shape': 3, 'slice': slice(0, 3)},
    'follow_left_ee_cartesian_pos':  {'name': 'follow1_pos', 'shape': 3, 'slice': slice(0, 3)},
    'master_left_ee_rotation':       {'name': 'follow1_pos', 'shape': 3, 'slice': slice(3, 6)},
    'follow_left_ee_rotation':       {'name': 'follow1_pos', 'shape': 3, 'slice': slice(3, 6)},
    'master_left_gripper':           {'name': 'follow1_pos', 'shape': 1, 'slice': slice(6, 7)},
    'follow_left_gripper':           {'name': 'follow1_pos', 'shape': 1, 'slice': slice(6, 7)},
    'master_right_ee_cartesian_pos': {'name': 'follow2_pos', 'shape': 3, 'slice': slice(0, 3)},
    'follow_right_ee_cartesian_pos': {'name': 'follow2_pos', 'shape': 3, 'slice': slice(0, 3)},
    'master_right_ee_rotation':      {'name': 'follow2_pos', 'shape': 3, 'slice': slice(3, 6)},
    'follow_right_ee_rotation':      {'name': 'follow2_pos', 'shape': 3, 'slice': slice(3, 6)},
    'master_right_gripper':          {'name': 'follow2_pos', 'shape': 1, 'slice': slice(6, 7)},
    'follow_right_gripper':          {'name': 'follow2_pos', 'shape': 1, 'slice': slice(6, 7)},

    # Gripper current (from robot's followX_joints_cur)
    'follow_left_gripper_cur':       {'name': 'follow1_joints_cur', 'shape': 1, 'slice': slice(-1, None)},
    'follow_right_gripper_cur':      {'name': 'follow2_joints_cur', 'shape': 1, 'slice': slice(-1, None)},

    # Other modalities
    'velocity_decomposed':           {'name': 'car_pose', 'shape': 3},
    'height':                        {'name': 'lift', 'shape': 1},
    'head_rotation':                 {'name': 'head_pos', 'shape': 2},
}

def build_action_masks(pred_keys, mapping):
    """Builds boolean masks for slicing the flat action tensor."""
    # Calculate the total dimension of the action space based on the keys the model predicts.
    total_dim = 0
    for k in pred_keys:
        if k in mapping:
            total_dim += mapping[k]['shape']
        else:
            # Optionally, log a warning for keys present in predict_action_keys but not in the mapping.
            # This can help debug configuration mismatches.
            # print(f"Warning: Predicted key '{k}' not found in MODEL_TO_ROBOT_MAPPING.")
            pass
    
    masks, cursor = {}, 0
    for k in pred_keys:
        if k not in mapping: continue
        dim = mapping[k]['shape']
        m = np.zeros(total_dim, dtype=bool)
        m[cursor:cursor + dim] = True
        masks[k] = m
        cursor += dim
    return masks


# ============================================================================
# 机器人物理限制和控制参数配置
# ============================================================================

# 高度限制
MIN_HEIGHT = 0.0  # m, 最小高度
MAX_HEIGHT = 0.4  # m, 最大高度

# 数值处理阈值
POSITION_ZERO_THRESHOLD = 0.001  # m, 位置零阈值

# 核心参数配置
TRIM_START = 5
TRIM_END = 25  # 统一截取长度
INFERENCE_STEPS = 100

# 机械臂轨迹参数
ARM_MAX_VELOCITY = 0.02
ARM_EXECUTION_HZ = 20
ARM_MIN_EXECUTION_TIME = 5.0
ARM_MAX_EXECUTION_TIME = 15.0

class UnifiedTrajectoryProcessor:
    """统一轨迹处理器"""
    
    @staticmethod
    def interpolate_trajectory_batch(trajectories, target_length, smooth=True):
        """
        批量插值多个轨迹到统一长度
        Args:
            trajectories: list of np.array, 每个数组shape为(N, D)
            target_length: int, 目标长度
            smooth: bool, 是否平滑
        Returns:
            list of np.array, 插值后的轨迹
        """
        if not trajectories:
            return []
        
        results = []
        for traj in trajectories:
            if len(traj) == 0:
                results.append(np.zeros((target_length, traj.shape[1])))
                continue
                
            if len(traj) == target_length:
                results.append(traj)
                continue
                
            # 向量化插值
            original_indices = np.linspace(0, len(traj) - 1, len(traj))
            target_indices = np.linspace(0, len(traj) - 1, target_length)
            
            # 处理不同类型的数据
            if traj.shape[1] == 7:  # 机械臂数据 [x,y,z,rx,ry,rz,gripper]
                interpolated = UnifiedTrajectoryProcessor._interpolate_arm_trajectory(
                    traj, original_indices, target_indices, target_length
                )
            elif traj.shape[1] == 3:  # 位置数据 [x,y,theta]
                interpolated = UnifiedTrajectoryProcessor._interpolate_position_trajectory(
                    traj, original_indices, target_indices, target_length
                )
            else:  # 其他数据(高度、电流等)
                interpolated = np.zeros((target_length, traj.shape[1]))
                for i in range(traj.shape[1]):
                    interpolated[:, i] = np.interp(target_indices, original_indices, traj[:, i])
            
            # 平滑处理
            if smooth and len(interpolated) >= 5:
                interpolated = UnifiedTrajectoryProcessor._smooth_trajectory(interpolated)
            
            results.append(interpolated)
        
        return results
    
    @staticmethod
    def _interpolate_arm_trajectory(traj, original_indices, target_indices, target_length):
        """优化的机械臂轨迹插值"""
        interpolated = np.zeros((target_length, 7))
        
        # 向量化插值位置和夹爪
        for i in [0, 1, 2, 6]:  # x, y, z, gripper
            interpolated[:, i] = np.interp(target_indices, original_indices, traj[:, i])
        
        # 四元数插值(向量化)
        quaternions = R.from_euler('xyz', traj[:, 3:6]).as_quat()
        interpolated_quats = np.zeros((target_length, 4))
        for i in range(4):
            interpolated_quats[:, i] = np.interp(target_indices, original_indices, quaternions[:, i])
        
        # 批量归一化
        norms = np.linalg.norm(interpolated_quats, axis=1, keepdims=True)
        interpolated_quats = interpolated_quats / norms
        
        # 批量转换回欧拉角
        interpolated[:, 3:6] = R.from_quat(interpolated_quats).as_euler('xyz')
        
        return interpolated
    
    @staticmethod
    def _interpolate_position_trajectory(traj, original_indices, target_indices, target_length):
        """优化的位置轨迹插值"""
        interpolated = np.zeros((target_length, 3))
        for i in range(3):
            interpolated[:, i] = np.interp(target_indices, original_indices, traj[:, i])
        return interpolated
    
    @staticmethod
    def _smooth_trajectory(trajectory):
        """向量化平滑处理"""
        if len(trajectory) < 5:
            return trajectory
        
        try:
            # 批量平滑所有维度
            smoothed = np.zeros_like(trajectory)
            for dim in range(trajectory.shape[1]):
                smoothed[:, dim] = savgol_filter(
                    trajectory[:, dim], 
                    min(5, len(trajectory) if len(trajectory) % 2 == 1 else len(trajectory) - 1), 
                    3, 
                    mode='nearest'
                )
            return smoothed
        except:
            return trajectory
    
    @staticmethod
    def calculate_optimal_trajectory_length(left_traj, right_traj):
        """计算最优轨迹长度"""
        # 向量化距离计算
        def calc_distance(traj):
            if len(traj) < 2:
                return 0.0
            pos_diff = traj[1:, :3] - traj[:-1, :3]
            return np.sum(np.linalg.norm(pos_diff, axis=1))
        
        distances = [calc_distance(left_traj), calc_distance(right_traj)]
        max_distance = max(distances)
        
        if max_distance > 1e-6:
            execution_time = np.clip(
                max_distance / ARM_MAX_VELOCITY, 
                ARM_MIN_EXECUTION_TIME, 
                ARM_MAX_EXECUTION_TIME
            )
        else:
            execution_time = ARM_MIN_EXECUTION_TIME
        
        return max(int(execution_time * ARM_EXECUTION_HZ), len(left_traj))


class VehiclePoseHandler:
    """车辆位姿和速度计算"""
    
    def __init__(self):
        self.current_pose = None
        self.previous_pose = None
        self.pose_history = deque(maxlen=10)
    
    def update_pose(self, new_pose):
        """更新车辆位姿"""
        if new_pose is not None:
            self.previous_pose = self.current_pose
            self.current_pose = np.array(new_pose)
            self.pose_history.append(self.current_pose.copy())
        return self.current_pose
    
    def velocity_to_pose(self, vx_body, vy_body, vyaw, dt, start_pose=None):
        """将本体坐标系速度转换为全局坐标系位置"""
        if start_pose is None:
            if self.current_pose is not None:
                start_pose = self.current_pose.copy()
            else:
                start_pose = np.array([0.0, 0.0, 0.0])
        
        x, y, theta = start_pose
        
        # 本体坐标系速度转换为全局坐标系位移
        cos_theta = np.cos(theta)
        sin_theta = np.sin(theta)
        
        # 坐标变换：本体坐标系 -> 全局坐标系
        dx_global = (vx_body * cos_theta - vy_body * sin_theta) * dt
        dy_global = (vx_body * sin_theta + vy_body * cos_theta) * dt
        dtheta = vyaw * dt
        
        # 计算新位置
        x_new = x + dx_global
        y_new = y + dy_global
        theta_new = theta + dtheta
        
        # 将角度限制在[-pi, pi]范围内
        theta_new = (theta_new + np.pi) % (2 * np.pi) - np.pi
        
        return np.array([x_new, y_new, theta_new])
    
    def compute_body_velocities_from_poses(self, current_pose, previous_pose, dt=1/20):
        """从位姿变化计算本体坐标系速度"""
        if current_pose is None or previous_pose is None:
            return np.array([0.0, 0.0, 0.0])
        
        # 计算全局坐标系下的位移
        dx_global = current_pose[0] - previous_pose[0]
        dy_global = current_pose[1] - previous_pose[1]
        dtheta = current_pose[2] - previous_pose[2]
        
        # 使用前一帧的角度进行坐标变换
        theta = previous_pose[2]
        cos_theta = np.cos(theta)
        sin_theta = np.sin(theta)
        
        # 全局坐标系位移转换为本体坐标系速度
        vx_body = (dx_global * cos_theta + dy_global * sin_theta) / dt
        vy_body = (-dx_global * sin_theta + dy_global * cos_theta) / dt
        vyaw = dtheta / dt
        
        return np.array([vx_body, vy_body, vyaw])


        
class Visualizer:
    """图像处理和保存"""
    
    def __init__(self, save_path):
        self.save_path = save_path
        os.makedirs(self.save_path, exist_ok=True)
        self.combined_img_dir = os.path.join(self.save_path, "combined_views")
        os.makedirs(self.combined_img_dir, exist_ok=True)
    
    def save_combined_image(self, image1, image2, image3, count):
        """保存三个摄像头图像的组合视图"""
        img1 = Image.fromarray(image1, mode='RGB')
        img2 = Image.fromarray(image2, mode='RGB')
        img3 = Image.fromarray(image3, mode='RGB')
        
        gap = 10
        total_width = img1.width + img2.width + img3.width + gap * 2
        max_height = max(img1.height, img2.height, img3.height)
        combined_img = Image.new('RGB', (total_width, max_height), color=(255, 255, 255))
        
        combined_img.paste(img1, (0, 0))
        combined_img.paste(img2, (img1.width + gap, 0))
        combined_img.paste(img3, (img1.width + img2.width + gap * 2, 0))
        
        timestamp = time.strftime("%Y%m%d_%H%M%S_%f")[:-3]
        path = os.path.join(self.combined_img_dir, f"combined_{timestamp}_{count}.jpg")
        combined_img.save(path)
        
        return path
    
    def process_images_for_model(self, image1, image2, image3, img_obs_horizon):
        """处理图像为模型输入格式"""
        def _replicate_image(img, horizon):
            if img is None:
                return None
            replicated_img = np.tile(img[np.newaxis, ...], (horizon, 1, 1, 1))
            return replicated_img

        camera_left = _replicate_image(image1, img_obs_horizon)
        camera_front = _replicate_image(image2, img_obs_horizon)
        camera_right = _replicate_image(image3, img_obs_horizon)
        
        return camera_front, camera_left, camera_right


def read_img_triton(byte_data):
    """图像读取函数"""
    if byte_data is None or len(byte_data) == 0:
        return None
    
    nparr = np.frombuffer(byte_data, np.uint8)
    image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    if image is None:
        raise ValueError("Image decompression failed")
    
    return image


class TritonPythonModel:
    def initialize(self, args):
        self.logger = pb_utils.Logger
        self.device_kind = args["model_instance_kind"]  # 设备类型：CPU 或 GPU
        self.device_id = args["model_instance_device_id"]  # 设备ID（如 "0" 表示 GPU 0）

        # 根据设备类型设置 PyTorch 的 device
        if self.device_kind == "GPU":
            self.torch_device = f"cuda:{self.device_id}"
        else:
            self.torch_device = "cpu"

        self.model_config = model_config = json.loads(args["model_config"])

        # 从环境变量或配置文件中获取参数
        ckpt_path = os.environ.get("CKPT_PATH") or str(model_config["parameters"].get("ckpt_path", {}).get("string_value", ""))
        
        # 这些参数不再使用，因为已经优化为自适应轨迹长度计算

        self.instruction = os.environ.get("INSTRUCTION") or model_config["parameters"].get("instruction", {}).get("string_value", None)
        
        self.control_mode = os.environ.get("CONTROL_MODE") or model_config["parameters"].get("control_mode", {}).get("string_value", "position")
        
        self.logger.log_info(f"checkpoint path: {ckpt_path}")
        self.logger.log_info(f"default control mode: {self.control_mode}")
        
        # 加载模型
        payload = torch.load(open(ckpt_path, "rb"), pickle_module=dill)
        cfg = payload["cfg"]
        cls = hydra.utils.get_class(cfg._target_)

        # Dynamically load keys from config
        self.obs_action_keys = list(cfg.task.obs_action_keys)
        self.predict_action_keys = list(cfg.task.predict_action_keys)
        self.logger.log_info(f"Loaded obs_action_keys: {self.obs_action_keys}")
        self.logger.log_info(f"Loaded predict_action_keys: {self.predict_action_keys}")
        self.predict_action_masks = build_action_masks(self.predict_action_keys, MODEL_TO_ROBOT_MAPPING)
        self.obs_action_dim = sum(MODEL_TO_ROBOT_MAPPING[key]['shape'] for key in self.obs_action_keys)

        # 解析观察键
        self.rgb_keys = list()
        self.lowdim_keys = list()
        obs_shape_meta = cfg.shape_meta.obs
        for key, attr in obs_shape_meta.items():
            type = attr.get("type", "low_dim")
            if type == "rgb":
                self.rgb_keys.append(key)
            elif type == "low_dim":
                self.lowdim_keys.append(key)

        # 设置工作空间 - 使用配置文件中的默认路径
        workspace = cls(cfg)
        workspace: BaseWorkspace
        workspace.load_payload(payload, exclude_keys=None, include_keys=None)
        
        # 获取策略模型
        self.policy: BaseImagePolicy = workspace.model
        self.policy.eval().to(self.torch_device)
        self.policy.num_inference_steps = INFERENCE_STEPS
        
        # 模型配置
        self.img_obs_horizon = cfg.task.img_obs_horizon
        # self.is_bi_mode = cfg.task.dataset.is_bi_mode
        self.use_gripper_cur = cfg.task.use_gripper_cur if hasattr(cfg.task, "use_gripper_cur") else False     
        
        # 添加电流数据配置的详细日志
        self.logger.log_info(f"[CONFIG] use_gripper_cur from config: {self.use_gripper_cur}")
        
        # 检查是否有环境变量覆盖电流数据使用
        env_use_gripper_cur = os.environ.get("USE_GRIPPER_CUR")
        if env_use_gripper_cur is not None:
            self.use_gripper_cur = env_use_gripper_cur.lower() in ['true', '1', 'yes', 'on']
            self.logger.log_info(f"[CONFIG] use_gripper_cur overridden by environment variable: {self.use_gripper_cur}")
        
        # 对于22维模型，建议启用电流数据
        if not self.use_gripper_cur:
            self.logger.log_info(f"[CONFIG] Warning: use_gripper_cur is False, but you have 22-dim model. Consider enabling it.")
            self.logger.log_info(f"[CONFIG] You can set environment variable USE_GRIPPER_CUR=true to enable it.")
        
        self.logger.log_info(f"[CONFIG] Final use_gripper_cur setting: {self.use_gripper_cur}")
        
        # 初始化车辆位姿处理器
        self.vehicle_pose_handler = VehiclePoseHandler()
        self.cur_velocity_decomposed = np.array([0.0, 0.0, 0.0])
        
        # 运行时状态
        self.predicted_base_velocity = None
        
        # 图像保存相关初始化
        self.image_save_dir = "/workspace/images"
        self.image_counter = 0
        self.visualizer = Visualizer(self.image_save_dir)
        
        self.logger.log_info("Model initialization completed")

    def _pred_func(self, views, inputs) -> dict:
        # 1. Update vehicle pose handler with current pose from input
        car_pose_data = inputs.get("car_pose", None)
        if car_pose_data is not None:
            self.vehicle_pose_handler.update_pose(car_pose_data)

        # 2. Build agent_pos from robot actions based on config
        obs_list = []
        for key in self.obs_action_keys:
            mapping_info = MODEL_TO_ROBOT_MAPPING[key]
            robot_key, data_slice = mapping_info['name'], mapping_info.get('slice')
            
            if robot_key not in inputs:
                raise ValueError(f"Required robot key '{robot_key}' for model key '{key}' not in received inputs")
            
            if key == 'velocity_decomposed':
                obs_list.append(self.cur_velocity_decomposed)
            else:
                raw_data = inputs[robot_key]
                if isinstance(raw_data, (float, int)):
                    raw_data = np.array([raw_data])
                
                # Ensure raw_data is a numpy array for slicing
                if not isinstance(raw_data, np.ndarray):
                    raw_data = np.array(raw_data)

                data_to_append = raw_data[data_slice] if data_slice else raw_data
                obs_list.append(data_to_append.flatten())

        agent_pos = np.concatenate(obs_list).reshape(1, -1)
        if agent_pos.shape[-1] != self.obs_action_dim:
            raise ValueError(f'agent_pos dim mismatch! Got {agent_pos.shape[-1]}, expected {self.obs_action_dim}')
        
        # 3. 准备观测数据
        obs_dict = {'agent_pos': agent_pos}
        view_mapping = {
            'face_view': views.get("camera_front"),
            'left_wrist_view': views.get("camera_left"),
            'right_wrist_view': views.get("camera_right"),
        }
        for key in self.rgb_keys:
            if key in view_mapping and view_mapping[key] is not None:
                obs_dict[key] = view_mapping[key]
            else:
                self.logger.log_info(f"Warning: View for model key '{key}' is missing from input.")

        # 4. 运行模型推理
        with torch.no_grad():
            batch_obs = {k: torch.from_numpy(v).unsqueeze(0).to(self.torch_device) for k, v in obs_dict.items()}
            batch = {'obs': batch_obs}
            
            # 添加指令（如果有）
            if self.instruction is not None:
                batch['instruction'] = [self.instruction]
            
            # 运行推理
            result = self.policy.predict_action(batch)
            action_pred = result['action_pred'][0].detach().to('cpu').numpy()
        
        # 5. Parse and process predicted actions
        parsed_actions = {key: action_pred[:, self.predict_action_masks[key]] for key in self.predict_action_keys if key in self.predict_action_masks}

        # Handle car movement
        current_car_pose = self.vehicle_pose_handler.current_pose if self.vehicle_pose_handler.current_pose is not None else np.array([0,0,0])
        num_steps = action_pred.shape[0]
        
        target_car_poses = np.tile(current_car_pose, (num_steps, 1)) # Default to no movement
        if 'velocity_decomposed' in parsed_actions:
            velocities = parsed_actions['velocity_decomposed']
            temp_poses = []
            pose_for_integration = current_car_pose.copy()
            for i in range(num_steps):
                pose_for_integration = self.vehicle_pose_handler.velocity_to_pose(
                    velocities[i,0], velocities[i,1], velocities[i,2], 1/ARM_EXECUTION_HZ, pose_for_integration
                )
                temp_poses.append(pose_for_integration)
            target_car_poses = np.array(temp_poses)
            self.cur_velocity_decomposed = velocities[-1]

        # Assemble arm actions
        def assemble_arm_action(side):
            pos_key = f'master_{side}_ee_cartesian_pos' if f'master_{side}_ee_cartesian_pos' in parsed_actions else f'follow_{side}_ee_cartesian_pos'
            rot_key = f'master_{side}_ee_rotation' if f'master_{side}_ee_rotation' in parsed_actions else f'follow_{side}_ee_rotation'
            grip_key = f'follow_{side}_gripper' if f'follow_{side}_gripper' in parsed_actions else f'master_{side}_gripper'
            
            parts = []
            for key in [pos_key, rot_key, grip_key]:
                if key in parsed_actions:
                    parts.append(parsed_actions[key])
            return np.concatenate(parts, axis=1) if parts else None

        left_traj_init = assemble_arm_action('left')
        right_traj_init = assemble_arm_action('right')
        
        # Determine optimal trajectory length based on arm movements
        # Use initial un-interpolated trajectories for distance calculation
        optimal_length = UnifiedTrajectoryProcessor.calculate_optimal_trajectory_length(
            left_traj_init if left_traj_init is not None else np.array([]), 
            right_traj_init if right_traj_init is not None else np.array([])
        )

        # Interpolate all predicted actions to the optimal length
        trajectories_to_interpolate = []
        # Add arm trajectories if they exist
        if left_traj_init is not None: trajectories_to_interpolate.append(left_traj_init)
        if right_traj_init is not None: trajectories_to_interpolate.append(right_traj_init)
        
        # Add other modalities if predicted
        modalities_to_interpolate = ['height', 'head_rotation']
        for mod in modalities_to_interpolate:
            if mod in parsed_actions:
                trajectories_to_interpolate.append(parsed_actions[mod])
        
        # Always add car pose for interpolation
        trajectories_to_interpolate.append(target_car_poses)

        interpolated_results = UnifiedTrajectoryProcessor.interpolate_trajectory_batch(
            trajectories_to_interpolate, optimal_length, smooth=True
        )
        
        # 6. Create response data from interpolated results
        data_dir = {}
        result_idx = 0
        
        # Populate arm data
        default_left_arm = inputs['follow1_pos'].flatten().tolist()
        default_right_arm = inputs['follow2_pos'].flatten().tolist()

        if left_traj_init is not None:
            data_dir['follow1_pos'] = interpolated_results[result_idx].tolist()
            result_idx += 1
        else:
            data_dir['follow1_pos'] = [default_left_arm] * optimal_length

        if right_traj_init is not None:
            data_dir['follow2_pos'] = interpolated_results[result_idx].tolist()
            result_idx += 1
        else:
            data_dir['follow2_pos'] = [default_right_arm] * optimal_length

        # Populate other modalities
        for mod in modalities_to_interpolate:
            if mod in parsed_actions:
                data_dir[mod] = interpolated_results[result_idx].tolist()
                result_idx += 1

        # Populate car pose
        data_dir['car_pose'] = interpolated_results[result_idx].tolist()

        # Final adjustments and formatting
        return self._create_response_data(
            data_dir, 
            default_left_arm=default_left_arm,
            default_right_arm=default_right_arm
        )

    def _create_response_data(self, result_data, default_left_arm, default_right_arm):
        """Creates the final response dictionary from processed data."""
        
        # Ensure arm data is present
        follow1_list = result_data.get('follow1_pos', [default_left_arm] * len(result_data.get('car_pose', [])))
        follow2_list = result_data.get('follow2_pos', [default_right_arm] * len(result_data.get('car_pose', [])))

        data_dir = {
            "follow1_pos": follow1_list,
            "follow2_pos": follow2_list,
            "follow1_joints": follow1_list,
            "follow2_joints": follow2_list,
        }
        
        # Head position
        if 'head_rotation' in result_data:
            data_dir['head_pos'] = result_data['head_rotation']
        else: # Default fixed head
            data_dir['head_pos'] = [[-1.0, 0.0]] * len(follow1_list)

        # Car position
        car_poses = []
        if 'car_pose' in result_data:
            for pose in result_data['car_pose']:
                pose_clean = np.array(pose)
                pose_clean[np.abs(pose_clean) < POSITION_ZERO_THRESHOLD] = 0.0
                car_poses.append([round(val, 2) for val in pose_clean])
        data_dir["car_pose"] = car_poses
        
        # Lift height
        height_list = []
        if 'height' in result_data:
            for h in result_data['height']:
                h_val = h[0] if isinstance(h, list) else h
                h_val = np.clip(h_val, MIN_HEIGHT, MAX_HEIGHT)
                if abs(h_val) < POSITION_ZERO_THRESHOLD: h_val = 0.0
                if h_val <= 0.05: h_val = 0.0
                height_list.append(round(h_val, 2))
        data_dir["lift"] = height_list
        
        self.logger.log_info(f"Position control: sending {len(car_poses)} global position points")
        
        # Default current data
        default_length = len(follow1_list)
        data_dir["follow1_joints_cur"] = [[0.0]] * default_length
        data_dir["follow2_joints_cur"] = [[0.0]] * default_length
        
        return data_dir

    def execute(self, requests):
        """处理推理请求，生成机械臂控制指令"""
        responses = []
        start_time = time.time()
        for request in requests:
            try:
                # 1. 从请求中提取必需的输入张量
                inputs = {
                    "follow1_pos": pb_utils.get_input_tensor_by_name(request, "ACTION_FOLLOW1_POS").as_numpy(),
                    "follow2_pos": pb_utils.get_input_tensor_by_name(request, "ACTION_FOLLOW2_POS").as_numpy(),
                    "follow1_joints_cur": pb_utils.get_input_tensor_by_name(request, "ACTION_FOLLOW1_JOINTS_CUR").as_numpy(),
                    "follow2_joints_cur": pb_utils.get_input_tensor_by_name(request, "ACTION_FOLLOW2_JOINTS_CUR").as_numpy(),
                }

                # 添加详细的输入调试信息
                self.logger.log_info(f"[DEBUG] Raw input tensors received:")
                self.logger.log_info(f"  ACTION_FOLLOW1_POS shape: {inputs['follow1_pos'].shape}, dtype: {inputs['follow1_pos'].dtype}")
                self.logger.log_info(f"  ACTION_FOLLOW2_POS shape: {inputs['follow2_pos'].shape}, dtype: {inputs['follow2_pos'].dtype}")
                self.logger.log_info(f"  ACTION_FOLLOW1_JOINTS_CUR shape: {inputs['follow1_joints_cur'].shape}, dtype: {inputs['follow1_joints_cur'].dtype}")
                self.logger.log_info(f"  ACTION_FOLLOW2_JOINTS_CUR shape: {inputs['follow2_joints_cur'].shape}, dtype: {inputs['follow2_joints_cur'].dtype}")

                # 2. 使用配置中的固定指令
                inputs["instruction"] = self.instruction
                if self.instruction:
                    self.logger.log_info(f"[DEBUG INPUT] instruction: {self.instruction}")
                else:
                    self.logger.log_info("[DEBUG INPUT] instruction: None")
                
                # 提取可选输入
                car_pose_tensor = pb_utils.get_input_tensor_by_name(request, "CAR_POSE")
                if car_pose_tensor:
                    inputs["car_pose"] = car_pose_tensor.as_numpy()
                    self.logger.log_info(f"[DEBUG] CAR_POSE shape: {inputs['car_pose'].shape}, values: {inputs['car_pose']}")
                
                lift_tensor = pb_utils.get_input_tensor_by_name(request, "LIFT")
                if lift_tensor:
                    inputs["lift"] = lift_tensor.as_numpy()
                    self.logger.log_info(f"[DEBUG] LIFT shape: {inputs['lift'].shape}, values: {inputs['lift']}")
                
                head_pos_tensor = pb_utils.get_input_tensor_by_name(request, "HEAD_POS")
                if head_pos_tensor:
                    inputs["head_pos"] = head_pos_tensor.as_numpy()
                    self.logger.log_info(f"[DEBUG] HEAD_POS shape: {inputs['head_pos'].shape}, values: {inputs['head_pos']}")
                
                control_mode_tensor = pb_utils.get_input_tensor_by_name(request, "CONTROL_MODE")
                if control_mode_tensor:
                    control_mode_str = control_mode_tensor.as_numpy()[0].decode('utf-8')
                    # 动态更新控制模式
                    self.control_mode = control_mode_str
                
                self.logger.log_info(f"[DEBUG INPUT] follow1_pos: {inputs['follow1_pos']}, follow2_pos: {inputs['follow2_pos']}")
                if "head_pos" in inputs:
                    self.logger.log_info(f"[DEBUG INPUT] head_pos: {inputs['head_pos']}")
                else:
                    self.logger.log_info("[DEBUG INPUT] head_pos: None (using default)")
                self.logger.log_info(f"Control mode: {self.control_mode}")

                # 2. 处理可选摄像头输入
                views = {}
                raw_images = {}  # 保存原始图像用于拼接
                
                for cam_name in ["CAMERA_LEFT", "CAMERA_FRONT", "CAMERA_RIGHT"]:
                    tensor = pb_utils.get_input_tensor_by_name(request, cam_name)
                    if not tensor:
                        continue  # 跳过不存在的可选输入
                        
                    # 获取字节流数据（形状为[1]的BYTES类型）
                    byte_data_array = tensor.as_numpy()
                    if byte_data_array.size == 0:
                        continue
                        
                    # 解压第一个元素（因为dims=[1]）
                    byte_data = byte_data_array[0]
                    try:
                        img = read_img_triton(byte_data)
                        if img is not None:
                            raw_images[cam_name] = img
                            self.logger.log_info(f"{cam_name} image: shape={img.shape}, pixel_range=[{img.min()}, {img.max()}], data_type={img.dtype}")
                    except Exception as e:
                        self.logger.log_error(f"Failed to process {cam_name} image: {str(e)}")

                # 处理图像数据
                if raw_images:
                    self.logger.log_info(f"Acquired view images: {list(raw_images.keys())}")
                    
                    left_img = raw_images.get("CAMERA_LEFT", None)
                    front_img = raw_images.get("CAMERA_FRONT", None)
                    right_img = raw_images.get("CAMERA_RIGHT", None)
                    
                    # 保存三视角拼接图像
                    self.image_counter += 1
                    if left_img is not None and front_img is not None and right_img is not None:
                        saved_path = self.visualizer.save_combined_image(left_img, front_img, right_img, self.image_counter)
                        self.logger.log_info(f"Saved three-view image: {saved_path}")
                    else:
                        self.logger.log_info("Some images are missing, skipping save")
                    
                    # 处理图像为模型输入格式
                    if left_img is not None and front_img is not None and right_img is not None:
                        camera_front, camera_left, camera_right = self.visualizer.process_images_for_model(
                            left_img, front_img, right_img, self.img_obs_horizon
                        )
                        views = {
                            "camera_left": camera_left,
                            "camera_front": camera_front,
                            "camera_right": camera_right,
                        }
                        self.logger.log_info("Using Visualizer to process images for model input format")
                else:
                    self.logger.log_info("No view images acquired, using default images")

                # 如果没有图像输入，创建默认的零图像
                if not views:
                    default_shape = (self.img_obs_horizon, 240, 320, 3)
                    default_image = np.zeros(default_shape, dtype=np.uint8)
                    views = {
                        "camera_left": default_image,
                        "camera_front": default_image,
                        "camera_right": default_image,
                    }
                    self.logger.log_info("Using default zero images")

                # 3. 执行预测
                with torch.no_grad():
                    result_data = self._pred_func(views, inputs)

                # 4. 构建输出张量
                output_tensors = []
                
                # 添加机械臂输出
                arm_outputs = ["FOLLOW1_POS", "FOLLOW2_POS", "FOLLOW1_JOINTS", "FOLLOW2_JOINTS"]
                for output_name in arm_outputs:
                    key = output_name.lower()
                    data = np.array(result_data[key], dtype=np.float32)
                    output_tensors.append(pb_utils.Tensor(output_name, data))
                
                # 添加头部输出
                head_data = np.array(result_data["head_pos"], dtype=np.float32)
                output_tensors.append(pb_utils.Tensor("HEAD_POS", head_data))
                
                # 添加高度输出
                lift_data = np.array(result_data["lift"], dtype=np.float32) if "lift" in result_data and result_data['lift'] else np.array([], dtype=np.float32)
                if lift_data.size > 0:
                    if lift_data.ndim == 1:
                        lift_data = lift_data.reshape(-1, 1)
                    output_tensors.append(pb_utils.Tensor("LIFT_OUT", lift_data))
                
                # 添加电流输出
                for joint_name, tensor_name in [("follow1_joints_cur", "FOLLOW1_JOINTS_CUR_OUT"), 
                                               ("follow2_joints_cur", "FOLLOW2_JOINTS_CUR_OUT")]:
                    if joint_name in result_data:
                        joint_data = np.array(result_data[joint_name], dtype=np.float32)
                        output_tensors.append(pb_utils.Tensor(tensor_name, joint_data))
                
                # 添加位置控制输出
                if "car_pose" in result_data:
                    car_pose_data = np.array(result_data["car_pose"], dtype=np.float32)
                    output_tensors.append(pb_utils.Tensor("CAR_POSE_OUT", car_pose_data))
                    self.logger.log_info("Output CAR_POSE_OUT (position mode)")

                # 5. 构造响应
                responses.append(pb_utils.InferenceResponse(output_tensors))

            except Exception as e:
                # 获取完整错误堆栈
                exc_type, exc_value, exc_tb = sys.exc_info()
                tb_lines = traceback.format_exception(exc_type, exc_value, exc_tb)
                full_traceback = "".join(tb_lines)
                relevant_stack = "\n".join([line.strip() for line in islice(full_traceback.split("\n"), 0, 15)])

                # 记录完整堆栈到服务端日志
                self.logger.log_error(f"Full error traceback:\n{full_traceback}")

                # 返回精简的客户端错误信息
                error_msg = f"Inference failed: {str(e)}\nRecent call stack:\n{str(relevant_stack)}"
                error = pb_utils.TritonError(error_msg)
                responses.append(pb_utils.InferenceResponse(error=error))
                
        output_duration = time.time() - start_time
        self.logger.log_info(f"Processed {len(requests)} requests in {output_duration:.2f} seconds")
        return responses
