name: "model"
backend: "python"

input [
  {
    name: "ACTION_FOLLOW1_POS"
    data_type: TYPE_FP32
    dims: [ 7 ]
  },
  {
    name: "ACTION_FOLLOW2_POS"
    data_type: TYPE_FP32
    dims: [ 7 ]
  },
  {
    name: "ACTION_FOLLOW1_JOINTS_CUR"
    data_type: TYPE_FP32
    dims: [ 7 ]
  },
  {
    name: "ACTION_FOLLOW2_JOINTS_CUR"
    data_type: TYPE_FP32
    dims: [ 7 ]
  },
  {
    name: "CAMERA_LEFT"
    data_type: TYPE_STRING
    dims: [ 1 ]
    optional: true
  },
  {
    name: "CAMERA_FRONT"
    data_type: TYPE_STRING
    dims: [ 1 ]
    optional: true
  },
  {
    name: "CAMERA_RIGHT"
    data_type: TYPE_STRING
    dims: [ 1 ]
    optional: true
  },
  {
    name: "INSTRUCTION"
    data_type: TYPE_STRING
    dims: [ 1 ]
    optional: true
  }
]
output [
  {
    name: "FOLLOW1_POS"
    data_type: TYPE_FP32
    dims: [ 20, 7 ]
  },
  {
    name: "FOLLOW2_POS"
    data_type: TYPE_FP32
    dims: [ 20, 7 ]
  },
  {
    name: "FOLLOW1_JOINTS"
    data_type: TYPE_FP32
    dims: [ 20, 7 ]
  },
  {
    name: "FOLLOW2_JOINTS"
    data_type: TYPE_FP32
    dims: [ 20, 7 ]
  }
]

parameters {
  key: "ckpt_path"
  value: {
    string_value: "${ckpt_path}"
  }
}

parameters {
  key: "action_interpolate_multiplier"
  value: {
    string_value: "${action_interpolate_multiplier}"
  }
}

parameters {
  key: "action_start_ratio"
  value: {
    string_value: "${action_start_ratio}"
  }
}

parameters {
  key: "action_end_ratio"
  value: {
    string_value: "${action_end_ratio}"
  }
}

parameters {
  key: "instruction"
  value: {
    string_value: "${instruction}"
  }
}

parameters {
  key: "engine_dir"
  value: {
    string_value: "${engine_dir}"
  }
}

parameters {
  key: "num_inference_steps"
  value: {
    string_value: "${num_inference_steps}"
  }
}

instance_group [
  {
    kind: KIND_GPU
    count: 1
    gpus: [0]
  }
]
