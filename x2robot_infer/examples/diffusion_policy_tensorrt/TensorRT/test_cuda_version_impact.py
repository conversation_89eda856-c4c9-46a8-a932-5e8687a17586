#!/usr/bin/env python3
"""
测试CUDA版本对FP16数值稳定性的影响
"""

import torch
import numpy as np

def test_fp16_operations():
    """测试基础的FP16操作稳定性"""
    
    print("🔍 CUDA版本对FP16数值稳定性影响测试")
    print("=" * 60)
    
    # 系统信息
    print(f"PyTorch CUDA版本: {torch.version.cuda}")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"GPU: {torch.cuda.get_device_name(0)}")
    print(f"CUDA能力: {torch.cuda.get_device_capability(0)}")
    print("-" * 60)
    
    # 测试用例：模拟UNet中可能出现的计算
    test_cases = [
        {
            'name': '基础矩阵乘法',
            'test_func': test_basic_matmul
        },
        {
            'name': '卷积操作',
            'test_func': test_conv_operations
        },
        {
            'name': '注意力机制',
            'test_func': test_attention_like
        },
        {
            'name': '批归一化',
            'test_func': test_batch_norm_like
        },
        {
            'name': '激活函数',
            'test_func': test_activation_functions
        }
    ]
    
    results = {}
    
    for test_case in test_cases:
        print(f"\n📊 测试: {test_case['name']}")
        try:
            result = test_case['test_func']()
            results[test_case['name']] = result
            status = "✅ 通过" if result['stable'] else "❌ 不稳定"
            print(f"   结果: {status}")
            if not result['stable']:
                print(f"   问题: {result.get('issue', 'Unknown')}")
        except Exception as e:
            print(f"   ❌ 错误: {e}")
            results[test_case['name']] = {'stable': False, 'error': str(e)}
    
    return results

def test_basic_matmul():
    """测试基础矩阵乘法的FP16稳定性"""
    
    # 创建测试数据 - 模拟UNet中的典型数值范围
    a_fp32 = torch.ones(100, 100, device='cuda') * 0.1
    b_fp32 = torch.ones(100, 100, device='cuda') * 0.1
    
    # FP32计算
    result_fp32 = torch.matmul(a_fp32, b_fp32)
    
    # FP16计算
    a_fp16 = a_fp32.half()
    b_fp16 = b_fp32.half()
    result_fp16 = torch.matmul(a_fp16, b_fp16).float()
    
    # 检查稳定性
    has_nan = torch.isnan(result_fp16).any()
    has_inf = torch.isinf(result_fp16).any()
    
    diff = torch.abs(result_fp32 - result_fp16).max()
    
    return {
        'stable': not (has_nan or has_inf),
        'max_diff': float(diff),
        'has_nan': bool(has_nan),
        'has_inf': bool(has_inf),
        'fp32_mean': float(result_fp32.mean()),
        'fp16_mean': float(result_fp16.mean())
    }

def test_conv_operations():
    """测试卷积操作的FP16稳定性"""
    
    # 模拟UNet中的卷积层
    conv = torch.nn.Conv1d(64, 128, 3, padding=1).cuda()
    
    # 测试数据
    x_fp32 = torch.ones(1, 64, 20, device='cuda') * 0.1
    
    # FP32计算
    with torch.no_grad():
        result_fp32 = conv(x_fp32)
    
    # FP16计算
    conv_fp16 = conv.half()
    x_fp16 = x_fp32.half()
    
    with torch.no_grad():
        result_fp16 = conv_fp16(x_fp16).float()
    
    # 检查稳定性
    has_nan = torch.isnan(result_fp16).any()
    has_inf = torch.isinf(result_fp16).any()
    
    return {
        'stable': not (has_nan or has_inf),
        'has_nan': bool(has_nan),
        'has_inf': bool(has_inf),
        'fp32_mean': float(result_fp32.mean()),
        'fp16_mean': float(result_fp16.mean())
    }

def test_attention_like():
    """测试注意力机制类似的操作"""
    
    # 模拟注意力计算
    q = torch.ones(1, 8, 64, device='cuda') * 0.1
    k = torch.ones(1, 8, 64, device='cuda') * 0.1
    v = torch.ones(1, 8, 64, device='cuda') * 0.1
    
    # FP32计算
    scores_fp32 = torch.matmul(q, k.transpose(-2, -1)) / (64 ** 0.5)
    attn_fp32 = torch.softmax(scores_fp32, dim=-1)
    result_fp32 = torch.matmul(attn_fp32, v)
    
    # FP16计算
    q_fp16 = q.half()
    k_fp16 = k.half()
    v_fp16 = v.half()
    
    scores_fp16 = torch.matmul(q_fp16, k_fp16.transpose(-2, -1)) / (64 ** 0.5)
    attn_fp16 = torch.softmax(scores_fp16, dim=-1)
    result_fp16 = torch.matmul(attn_fp16, v_fp16).float()
    
    # 检查稳定性
    has_nan = torch.isnan(result_fp16).any()
    has_inf = torch.isinf(result_fp16).any()
    
    return {
        'stable': not (has_nan or has_inf),
        'has_nan': bool(has_nan),
        'has_inf': bool(has_inf),
        'fp32_mean': float(result_fp32.mean()),
        'fp16_mean': float(result_fp16.mean())
    }

def test_batch_norm_like():
    """测试批归一化类似的操作"""
    
    # 模拟GroupNorm操作
    gn = torch.nn.GroupNorm(8, 64).cuda()
    
    x_fp32 = torch.ones(1, 64, 20, device='cuda') * 0.1
    
    # FP32计算
    with torch.no_grad():
        result_fp32 = gn(x_fp32)
    
    # FP16计算
    gn_fp16 = gn.half()
    x_fp16 = x_fp32.half()
    
    with torch.no_grad():
        result_fp16 = gn_fp16(x_fp16).float()
    
    # 检查稳定性
    has_nan = torch.isnan(result_fp16).any()
    has_inf = torch.isinf(result_fp16).any()
    
    return {
        'stable': not (has_nan or has_inf),
        'has_nan': bool(has_nan),
        'has_inf': bool(has_inf),
        'fp32_mean': float(result_fp32.mean()),
        'fp16_mean': float(result_fp16.mean())
    }

def test_activation_functions():
    """测试激活函数的FP16稳定性"""
    
    x = torch.ones(1000, device='cuda') * 0.1
    
    activations = {
        'SiLU': torch.nn.SiLU(),
        'GELU': torch.nn.GELU(),
        'ReLU': torch.nn.ReLU(),
    }
    
    results = {}
    
    for name, activation in activations.items():
        activation = activation.cuda()
        
        # FP32
        result_fp32 = activation(x)
        
        # FP16
        activation_fp16 = activation.half()
        x_fp16 = x.half()
        result_fp16 = activation_fp16(x_fp16).float()
        
        has_nan = torch.isnan(result_fp16).any()
        has_inf = torch.isinf(result_fp16).any()
        
        results[name] = {
            'stable': not (has_nan or has_inf),
            'has_nan': bool(has_nan),
            'has_inf': bool(has_inf)
        }
    
    # 总体稳定性
    overall_stable = all(r['stable'] for r in results.values())
    
    return {
        'stable': overall_stable,
        'details': results
    }

if __name__ == "__main__":
    results = test_fp16_operations()
    
    print("\n" + "=" * 60)
    print("📈 总体结果分析:")
    print("-" * 60)
    
    stable_tests = sum(1 for r in results.values() if r.get('stable', False))
    total_tests = len(results)
    
    print(f"稳定测试: {stable_tests}/{total_tests}")
    
    if stable_tests < total_tests:
        print(f"\n❌ 检测到FP16数值稳定性问题")
        print(f"建议升级CUDA到12.9+版本")
    else:
        print(f"\n✅ FP16数值稳定性良好")
