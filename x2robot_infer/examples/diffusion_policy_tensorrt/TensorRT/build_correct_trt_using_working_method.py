#!/usr/bin/env python3
"""
TensorRT引擎构建器
"""

import os
import sys
import time
import torch
import numpy as np
import tensorrt as trt
from pathlib import Path
import traceback
import argparse

# 添加项目根目录到路径
ROOT_DIR = str(Path(__file__).parent.parent)
sys.path.append(ROOT_DIR)

# TensorRT Logger
TRT_LOGGER = trt.Logger(trt.Logger.WARNING)

class CorrectTensorRTBuilderUsingWorkingMethod:
    """使用已验证的方法构建正确的TensorRT引擎"""
    
    def __init__(self,
                 onnx_dir: str = "correct_onnx_models",
                 engine_dir: str = "correct_trt_engines_working_method",
                 checkpoint_path: str = None,
                 device: str = 'cuda',
                 precision: str = 'fp32'):
        """
        初始化构建器

        Args:
            onnx_dir: 正确的ONNX模型目录
            engine_dir: TensorRT引擎输出目录
            checkpoint_path: PyTorch checkpoint路径（用于验证）
            device: 运行设备
            precision: 模型精度，'fp32' 或 'fp16'
        """
        self.onnx_dir = onnx_dir
        self.engine_dir = engine_dir
        self.checkpoint_path = checkpoint_path
        self.device = device
        self.precision = precision

        # 创建输出目录
        os.makedirs(engine_dir, exist_ok=True)

        # 定义模型配置（基于成功的转换器配置）
        # 使用混合精度策略确保数值稳定性
        obs_precision = precision if precision == 'fp32' else 'fp16'  # obs_encoder可以安全使用FP16
        unet_precision = 'fp32'  # UNet强制使用FP32以避免数值不稳定

        if precision == 'fp16':
            print(f"   ⚠️  使用混合精度策略确保稳定性:")
            print(f"      obs_encoder: {obs_precision}")
            print(f"      unet: {unet_precision} (避免数值不稳定)")

        self.model_configs = {
            'obs_encoder': {
                'onnx_file': 'obs_encoder_complete.onnx',
                'engine_file': 'obs_encoder.trt',
                'input_shapes': {
                    'face_view': (1, 1, 3, 480, 640),
                    'left_wrist_view': (1, 1, 3, 480, 640),
                    'right_wrist_view': (1, 1, 3, 480, 640),
                    'agent_pos': (1, 1, 14)
                },
                'max_batch_size': 4,  # 保守的批次大小
                'precision': obs_precision
            },
            'unet': {
                'onnx_file': 'unet_complete.onnx',
                'engine_file': 'unet.trt',
                'input_shapes': {
                    'sample': (1, 20, 14),
                    'timestep': (1,),
                    'global_cond': (1, 6158)
                },
                'max_batch_size': 2,  # 保守的批次大小
                'precision': unet_precision
            }
        }
        
        print(f"✅ 构建器初始化完成")
        print(f"   ONNX目录: {onnx_dir}")
        print(f"   引擎目录: {engine_dir}")
        print(f"   精度设置: {precision}")
    
    def build_engine(self,
                    onnx_path: str,
                    engine_path: str,
                    input_shapes: dict = None,
                    max_batch_size: int = 1,
                    precision: str = 'fp32',
                    max_workspace_size: int = 1 << 30,  # 1GB
                    optimization_level: int = 3,
                    verbose: bool = True) -> bool:
        """
        使用已验证的方法构建TensorRT引擎
        """
        try:
            if verbose:
                print(f"\n=== 构建TensorRT引擎 ===")
                print(f"   ONNX文件: {onnx_path}")
                print(f"   引擎文件: {engine_path}")
                print(f"   精度: {precision}")
                print(f"   最大批次: {max_batch_size}")
            
            # 1. 创建builder和config
            builder = trt.Builder(TRT_LOGGER)
            config = builder.create_builder_config()
            
            # 2. 设置精度（支持FP32和FP16）
            if precision == 'fp32':
                # 禁用TF32以确保FP32精度
                if builder.platform_has_tf32:
                    print("   禁用TF32以确保FP32精度")
                # 设置严格数值检查
                config.set_flag(trt.BuilderFlag.STRICT_NANS)
                print("   启用STRICT_NANS以确保FP32精度")
            elif precision == 'fp16':
                # 启用FP16精度
                if builder.platform_has_fast_fp16:
                    config.set_flag(trt.BuilderFlag.FP16)
                    print("   启用FP16精度")
                else:
                    print("   ⚠️ 当前平台不支持FP16，回退到FP32")
            else:
                print(f"   ⚠️ 不支持的精度: {precision}，使用FP32")
            
            # 3. 设置优化级别和工作空间
            config.builder_optimization_level = optimization_level
            config.set_memory_pool_limit(trt.MemoryPoolType.WORKSPACE, max_workspace_size)
            
            # 4. 创建网络（使用EXPLICIT_BATCH）
            network = builder.create_network(1 << int(trt.NetworkDefinitionCreationFlag.EXPLICIT_BATCH))
            
            # 5. 解析ONNX模型
            parser = trt.OnnxParser(network, TRT_LOGGER)
            
            with open(onnx_path, 'rb') as model:
                if not parser.parse(model.read()):
                    print("   ❌ ONNX解析失败:")
                    for error in range(parser.num_errors):
                        print(f"      {parser.get_error(error)}")
                    return False
            
            print("   ✅ ONNX解析成功")
            
            # 6. 配置optimization profile（关键步骤）
            if input_shapes:
                profile = builder.create_optimization_profile()
                
                for i in range(network.num_inputs):
                    input_tensor = network.get_input(i)
                    input_name = input_tensor.name
                    
                    if input_name in input_shapes:
                        shape = input_shapes[input_name]
                        
                        # 检查输入是否为动态
                        is_dynamic = any(dim == -1 for dim in input_tensor.shape)
                        
                        if is_dynamic:
                            # 动态输入：设置最小/最优/最大形状
                            min_shape = list(shape)
                            opt_shape = list(shape)
                            max_shape = list(shape)
                            
                            # 如果第一个维度是批处理维度，设置动态批处理
                            if len(shape) > 0:
                                min_shape[0] = 1
                                opt_shape[0] = max(1, max_batch_size // 2)
                                max_shape[0] = max_batch_size
                            
                            profile.set_shape(input_name, min_shape, opt_shape, max_shape)
                            
                            if verbose:
                                print(f"   动态输入 '{input_name}': min={min_shape}, opt={opt_shape}, max={max_shape}")
                        else:
                            # 静态输入：使用固定形状
                            profile.set_shape(input_name, shape, shape, shape)
                            
                            if verbose:
                                print(f"   静态输入 '{input_name}': shape={shape}")
                
                config.add_optimization_profile(profile)
                print("   ✅ Optimization profile配置完成")

            # 6.5. 层级精度控制已禁用
            # if precision == 'fp16' and 'unet' in onnx_path.lower():
            #     print("   应用UNet层级精度控制...")
            #     self._apply_layer_precision_constraints(network, config)

            # 7. 构建引擎
            if verbose:
                print("   开始构建TensorRT引擎...")
                start_time = time.time()
            
            serialized_engine = builder.build_serialized_network(network, config)
            
            if serialized_engine is None:
                print("   ❌ TensorRT引擎构建失败")
                return False
            
            # 8. 保存引擎到文件
            os.makedirs(os.path.dirname(engine_path), exist_ok=True)
            with open(engine_path, 'wb') as f:
                f.write(bytes(serialized_engine))
            
            if verbose:
                build_time = time.time() - start_time
                engine_size = serialized_engine.nbytes / (1024 * 1024)  # MB
                print(f"   ✅ TensorRT引擎构建成功!")
                print(f"      构建时间: {build_time:.2f}s")
                print(f"      引擎大小: {engine_size:.2f}MB")
                print(f"      保存到: {engine_path}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 引擎构建失败: {e}")
            traceback.print_exc()
            return False
    
    def validate_engine(self, engine_path: str, verbose: bool = True) -> bool:
        """验证TensorRT引擎（使用成功的验证方法）"""
        try:
            if not os.path.exists(engine_path):
                print(f"   引擎文件不存在: {engine_path}")
                return False
            
            # 加载引擎
            runtime = trt.Runtime(TRT_LOGGER)
            with open(engine_path, 'rb') as f:
                engine = runtime.deserialize_cuda_engine(f.read())
            
            if engine is None:
                print("   ❌ TensorRT引擎反序列化失败")
                return False
            
            # 创建执行上下文
            context = engine.create_execution_context()
            
            if verbose:
                print(f"   ✅ 引擎验证成功: {os.path.basename(engine_path)}")
                print(f"      I/O张量数量: {engine.num_io_tensors}")
                for i in range(engine.num_io_tensors):
                    name = engine.get_tensor_name(i)
                    mode = engine.get_tensor_mode(name)
                    dtype = engine.get_tensor_dtype(name)
                    shape = engine.get_tensor_shape(name)
                    io_type = 'Input' if mode == trt.TensorIOMode.INPUT else 'Output'
                    print(f"      {io_type} {i}: {name}, shape={shape}, dtype={dtype}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 引擎验证失败: {e}")
            traceback.print_exc()
            return False
    
    def test_engine_inference(self, engine_path: str, model_name: str) -> bool:
        """测试引擎推理"""
        print(f"\n=== 测试引擎推理: {model_name} ===")
        
        try:
            from tensorrt_diffusion_pipeline import TensorRTEngine
            
            engine = TensorRTEngine(engine_path)
            
            # 创建测试输入
            if model_name == 'obs_encoder':
                test_inputs = {
                    'face_view': np.ones((1, 1, 3, 480, 640), dtype=np.float32) * 0.5,
                    'left_wrist_view': np.ones((1, 1, 3, 480, 640), dtype=np.float32) * 0.5,
                    'right_wrist_view': np.ones((1, 1, 3, 480, 640), dtype=np.float32) * 0.5,
                    'agent_pos': np.zeros((1, 1, 14), dtype=np.float32)
                }
            else:  # unet
                test_inputs = {
                    'sample': np.random.randn(1, 20, 14).astype(np.float32),
                    'timestep': np.array([5], dtype=np.int64),
                    'global_cond': np.random.randn(1, 6158).astype(np.float32)
                }
            
            # 运行推理
            outputs = engine.infer(test_inputs)
            
            print(f"   ✅ {model_name}推理成功")
            for name, output in outputs.items():
                # 确保output是numpy数组
                if hasattr(output, 'cpu'):
                    output_np = output.cpu().numpy()
                else:
                    output_np = np.array(output)
                print(f"      输出 {name}: shape={output_np.shape}, mean={np.mean(output_np):.6f}, std={np.std(output_np):.6f}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ {model_name}推理失败: {e}")
            traceback.print_exc()
            return False

    def build_all_engines(self) -> tuple:
        """构建所有TensorRT引擎"""
        print(f"=== 开始构建所有TensorRT引擎 ===")

        results = {}
        engine_paths = {}

        for model_name, config in self.model_configs.items():
            onnx_file = os.path.join(self.onnx_dir, config['onnx_file'])
            engine_file = os.path.join(self.engine_dir, config['engine_file'])

            if not os.path.exists(onnx_file):
                print(f"⚠️ ONNX文件不存在: {onnx_file}")
                results[model_name] = False
                continue

            print(f"\n--- 构建 {model_name} ---")

            success = self.build_engine(
                onnx_path=onnx_file,
                engine_path=engine_file,
                input_shapes=config['input_shapes'],
                max_batch_size=config['max_batch_size'],
                precision=config['precision'],
                verbose=True
            )

            results[model_name] = success
            if success:
                engine_paths[model_name] = engine_file
                print(f"✅ {model_name} 构建成功")
            else:
                print(f"❌ {model_name} 构建失败")

        return results, engine_paths

    def validate_all_engines(self, engine_paths: dict) -> dict:
        """验证所有TensorRT引擎"""
        print(f"\n=== 验证所有TensorRT引擎 ===")

        validation_results = {}

        for model_name, engine_path in engine_paths.items():
            print(f"\n--- 验证 {model_name} ---")

            # 1. 基本验证
            basic_valid = self.validate_engine(engine_path, verbose=True)

            # 2. 推理测试
            inference_valid = False
            if basic_valid:
                inference_valid = self.test_engine_inference(engine_path, model_name)

            validation_results[model_name] = {
                'basic_validation': basic_valid,
                'inference_test': inference_valid,
                'overall': basic_valid and inference_valid
            }

            if validation_results[model_name]['overall']:
                print(f"✅ {model_name} 验证完全通过")
            else:
                print(f"❌ {model_name} 验证失败")

        return validation_results

    def validate_vs_pytorch(self, engine_paths: dict) -> bool:
        """验证TensorRT引擎与PyTorch模型的一致性"""
        print(f"\n=== 验证TensorRT vs PyTorch一致性 ===")

        if not self.checkpoint_path:
            print(f"   ⚠️ 未提供checkpoint路径，跳过验证")
            return True

        try:
            # 1. 加载原始PyTorch模型
            from test_tensorrt_pipeline import create_dummy_policy

            pytorch_model = create_dummy_policy(self.checkpoint_path)
            if pytorch_model is None:
                print(f"   ❌ PyTorch模型加载失败")
                return False

            pytorch_model = pytorch_model.to(self.device).eval()
            print(f"   ✅ PyTorch模型加载成功")

            # 2. 分别验证obs_encoder和unet
            all_passed = True

            # 验证obs_encoder
            if 'obs_encoder' in engine_paths:
                obs_encoder_passed = self._validate_obs_encoder_vs_pytorch(
                    engine_paths['obs_encoder'],
                    pytorch_model
                )
                all_passed = all_passed and obs_encoder_passed

            # 验证unet
            if 'unet' in engine_paths:
                unet_passed = self._validate_unet_vs_pytorch(
                    engine_paths['unet'],
                    pytorch_model
                )
                all_passed = all_passed and unet_passed

            if all_passed:
                print(f"\n✅ 所有TensorRT引擎与PyTorch模型一致性验证通过")
            else:
                print(f"\n❌ 部分TensorRT引擎与PyTorch模型存在差异")

            return all_passed

        except Exception as e:
            print(f"   ❌ 验证过程失败: {e}")
            traceback.print_exc()
            return False

    def _validate_obs_encoder_vs_pytorch(self, obs_encoder_engine_path: str, pytorch_model) -> bool:
        """验证obs_encoder TensorRT引擎与PyTorch模型的一致性"""
        print(f"\n--- 验证obs_encoder一致性 ---")

        try:
            # 1. 加载TensorRT引擎
            from tensorrt_diffusion_pipeline import TensorRTEngine
            obs_encoder_engine = TensorRTEngine(obs_encoder_engine_path)

            # 2. 创建测试数据
            test_cases = [
                {
                    'name': '固定输入（图像=0.5，agent_pos=0）',
                    'inputs': {
                        'face_view': torch.ones(1, 1, 3, 480, 640, device=self.device) * 0.5,
                        'left_wrist_view': torch.ones(1, 1, 3, 480, 640, device=self.device) * 0.5,
                        'right_wrist_view': torch.ones(1, 1, 3, 480, 640, device=self.device) * 0.5,
                        'agent_pos': torch.zeros(1, 1, 14, device=self.device)
                    }
                },
                {
                    'name': '全零输入',
                    'inputs': {
                        'face_view': torch.zeros(1, 1, 3, 480, 640, device=self.device),
                        'left_wrist_view': torch.zeros(1, 1, 3, 480, 640, device=self.device),
                        'right_wrist_view': torch.zeros(1, 1, 3, 480, 640, device=self.device),
                        'agent_pos': torch.zeros(1, 1, 14, device=self.device)
                    }
                }
            ]

            all_passed = True

            for test_case in test_cases:
                print(f"   测试案例: {test_case['name']}")
                inputs = test_case['inputs']

                # PyTorch推理（使用原始模型的完整流程）
                with torch.no_grad():
                    # 应用normalizer（忽略RGB keys）
                    nobs = pytorch_model.normalizer.normalize(inputs, ignore_keys=pytorch_model.rgb_keys)
                    # 移除instruction键（如果存在）
                    this_nobs = {k: v for k, v in nobs.items() if k != 'instruction'}
                    # 通过obs_encoder
                    pytorch_output = pytorch_model.obs_encoder(this_nobs)
                    # 展平输出
                    pytorch_output = pytorch_output.reshape(pytorch_output.shape[0], -1)

                # TensorRT推理
                trt_inputs = {
                    'face_view': inputs['face_view'].cpu().numpy().astype(np.float32),
                    'left_wrist_view': inputs['left_wrist_view'].cpu().numpy().astype(np.float32),
                    'right_wrist_view': inputs['right_wrist_view'].cpu().numpy().astype(np.float32),
                    'agent_pos': inputs['agent_pos'].cpu().numpy().astype(np.float32)
                }

                trt_outputs = obs_encoder_engine.infer(trt_inputs)
                trt_output = trt_outputs['encoded_obs']

                # 确保是torch tensor
                if not isinstance(trt_output, torch.Tensor):
                    trt_output = torch.from_numpy(trt_output).to(self.device)
                else:
                    trt_output = trt_output.to(self.device)

                # 比较结果
                diff = torch.abs(pytorch_output - trt_output)
                mse = torch.mean(diff ** 2).item()
                mae = torch.mean(diff).item()
                max_diff = torch.max(diff).item()

                print(f"     PyTorch输出: mean={torch.mean(pytorch_output).item():.6f}, std={torch.std(pytorch_output).item():.6f}")
                print(f"     TensorRT输出: mean={torch.mean(trt_output).item():.6f}, std={torch.std(trt_output).item():.6f}")
                print(f"     差异: MSE={mse:.8f}, MAE={mae:.8f}, Max={max_diff:.8f}")

                # 判断是否通过
                if mse < 1e-6 and max_diff < 1e-5:
                    print(f"     ✅ 通过（高精度一致）")
                elif mse < 1e-4 and max_diff < 1e-2:
                    print(f"     ✅ 通过（可接受精度）")
                elif mse < 1e-2 and max_diff < 1e-1:
                    print(f"     ✅ 通过（低精度但可用）")
                else:
                    print(f"     ❌ 失败（精度不足）")
                    all_passed = False

            if all_passed:
                print(f"   ✅ obs_encoder一致性验证通过")
            else:
                print(f"   ❌ obs_encoder一致性验证失败")

            return all_passed

        except Exception as e:
            print(f"   ❌ obs_encoder验证失败: {e}")
            traceback.print_exc()
            return False

    def _validate_unet_vs_pytorch(self, unet_engine_path: str, pytorch_model) -> bool:
        """验证UNet TensorRT引擎与PyTorch模型的一致性"""
        print(f"\n--- 验证UNet一致性 ---")

        try:
            # 1. 加载TensorRT引擎
            from tensorrt_diffusion_pipeline import TensorRTEngine
            unet_engine = TensorRTEngine(unet_engine_path)

            # 2. 获取原始UNet模型
            unet_model = pytorch_model.model
            unet_model.eval()

            # 3. 创建确定性测试数据（使用固定种子）
            torch.manual_seed(42)
            np.random.seed(42)

            test_cases = [
                {
                    'name': '确定性输入1',
                    'inputs': {
                        'sample': torch.randn(1, 20, 14, device=self.device),
                        'timestep': torch.tensor([5], dtype=torch.long, device=self.device),
                        'global_cond': torch.randn(1, 6158, device=self.device)
                    }
                },
                {
                    'name': '零时间步',
                    'inputs': {
                        'sample': torch.zeros(1, 20, 14, device=self.device),
                        'timestep': torch.tensor([0], dtype=torch.long, device=self.device),
                        'global_cond': torch.zeros(1, 6158, device=self.device)
                    }
                },
                {
                    'name': '确定性输入2',
                    'inputs': {
                        'sample': torch.ones(1, 20, 14, device=self.device) * 0.1,
                        'timestep': torch.tensor([10], dtype=torch.long, device=self.device),
                        'global_cond': torch.ones(1, 6158, device=self.device) * 0.5
                    }
                }
            ]

            all_passed = True

            for test_case in test_cases:
                print(f"   测试案例: {test_case['name']}")
                inputs = test_case['inputs']

                sample = inputs['sample']
                timestep = inputs['timestep']
                global_cond = inputs['global_cond']

                # PyTorch推理（使用原始UNet）
                with torch.no_grad():
                    pytorch_output = unet_model(sample, timestep, global_cond=global_cond)

                # TensorRT推理
                trt_inputs = {
                    'sample': sample.cpu().numpy().astype(np.float32),
                    'timestep': timestep.cpu().numpy().astype(np.int64),
                    'global_cond': global_cond.cpu().numpy().astype(np.float32)
                }

                trt_outputs = unet_engine.infer(trt_inputs)
                trt_output = trt_outputs['noise_pred']

                # 确保是torch tensor
                if not isinstance(trt_output, torch.Tensor):
                    trt_output = torch.from_numpy(trt_output).to(self.device)
                else:
                    trt_output = trt_output.to(self.device)

                # 比较结果
                diff = torch.abs(pytorch_output - trt_output)
                mse = torch.mean(diff ** 2).item()
                mae = torch.mean(diff).item()
                max_diff = torch.max(diff).item()

                print(f"     PyTorch输出: mean={torch.mean(pytorch_output).item():.6f}, std={torch.std(pytorch_output).item():.6f}")
                print(f"     TensorRT输出: mean={torch.mean(trt_output).item():.6f}, std={torch.std(trt_output).item():.6f}")
                print(f"     差异: MSE={mse:.8f}, MAE={mae:.8f}, Max={max_diff:.8f}")

                # 判断是否通过
                if mse < 1e-6 and max_diff < 1e-5:
                    print(f"     ✅ 通过（高精度一致）")
                elif mse < 1e-4 and max_diff < 1e-2:
                    print(f"     ✅ 通过（可接受精度）")
                elif mse < 1e-2 and max_diff < 1e-1:
                    print(f"     ✅ 通过（低精度但可用）")
                else:
                    print(f"     ❌ 失败（精度不足）")
                    all_passed = False

            if all_passed:
                print(f"   ✅ UNet一致性验证通过")
            else:
                print(f"   ❌ UNet一致性验证失败")

            return all_passed

        except Exception as e:
            print(f"   ❌ UNet验证失败: {e}")
            traceback.print_exc()
            return False

    def _apply_layer_precision_constraints(self, network, config):
        """为UNet网络的特定层应用精度约束，解决FP16数值稳定性问题"""
        try:
            print("     分析网络层并应用精度约束...")

            # 统计不同类型的层
            layer_stats = {
                'total_layers': network.num_layers,
                'fp32_constrained': 0,
                'fp16_allowed': 0
            }

            # 遍历所有层，为数值敏感的层设置FP32精度
            for i in range(network.num_layers):
                layer = network.get_layer(i)
                layer_name = layer.name
                layer_type = layer.type

                # 判断是否为数值敏感层
                needs_fp32 = self._is_numerically_sensitive_layer(layer_name, layer_type)

                if needs_fp32:
                    # 为数值敏感层设置FP32精度约束 (TensorRT 10.x兼容)
                    try:
                        layer.precision = trt.DataType.FLOAT
                        # 检查层是否有输出
                        if layer.num_outputs > 0:
                            layer.set_output_type(0, trt.DataType.FLOAT)
                        layer_stats['fp32_constrained'] += 1
                        print(f"     层 {i}: {layer_name} ({layer_type}) -> FP32")
                    except Exception as e:
                        print(f"     ⚠️ 无法为层 {i} 设置精度约束: {e}")
                        layer_stats['fp16_allowed'] += 1
                else:
                    # 允许其他层使用FP16
                    layer_stats['fp16_allowed'] += 1

            print(f"     精度约束统计:")
            print(f"       总层数: {layer_stats['total_layers']}")
            print(f"       FP32约束层: {layer_stats['fp32_constrained']}")
            print(f"       FP16允许层: {layer_stats['fp16_allowed']}")

        except Exception as e:
            print(f"     ⚠️ 层级精度控制应用失败: {e}")
            # 不抛出异常，继续构建过程

    def _is_numerically_sensitive_layer(self, layer_name, layer_type):
        """判断层是否对数值敏感，需要使用FP32精度"""

        # 首先排除不能更改精度的层类型
        excluded_layer_types = [
            trt.LayerType.SHAPE,           # Shape层必须使用Int64
            trt.LayerType.CONSTANT,        # 常量层有固定数据类型
            trt.LayerType.CAST,            # 类型转换层
            trt.LayerType.GATHER,          # 索引操作
            trt.LayerType.SLICE,           # 切片操作
            trt.LayerType.CONCATENATION,   # 拼接操作（通常不涉及数值计算）
            trt.LayerType.SHUFFLE,         # 重排操作
            trt.LayerType.UNSQUEEZE,       # 维度操作
        ]

        if layer_type in excluded_layer_types:
            return False

        # 基于层名称的模式匹配（更精确的模式）
        sensitive_name_patterns = [
            '/sin',                 # 正弦函数
            '/cos',                 # 余弦函数
            '/exp',                 # 指数运算
            '/log',                 # 对数运算
            'softplus',             # Softplus激活
            'tanh',                 # Tanh激活
            '/mul',                 # 乘法运算（FiLM调制）
            '/add',                 # 加法运算（FiLM调制）
            'diffusion_step_encoder', # 扩散步骤编码
            'cond_encoder',         # 条件编码器
        ]

        # 检查层名称（更严格的匹配）
        layer_name_lower = layer_name.lower()
        for pattern in sensitive_name_patterns:
            if pattern in layer_name_lower:
                return True

        # 基于层类型的判断（更保守的策略）
        if layer_type == trt.LayerType.UNARY:
            # 只有特定的一元运算需要FP32
            if any(op in layer_name_lower for op in ['sin', 'cos', 'exp', 'log', 'softplus', 'tanh']):
                return True

        if layer_type == trt.LayerType.ELEMENTWISE:
            # 只有涉及FiLM调制的元素级运算需要FP32
            if any(op in layer_name_lower for op in ['mul', 'add']) and 'cond_encoder' in layer_name_lower:
                return True

        if layer_type == trt.LayerType.ACTIVATION:
            # 特定的激活函数需要FP32
            if any(act in layer_name_lower for act in ['softplus', 'tanh', 'mish']):
                return True

        return False

    def run_complete_workflow(self) -> bool:
        """运行完整的构建和验证工作流"""
        print(f"🚀 开始完整的TensorRT引擎构建工作流")
        print(f"   使用已验证的转换方法")
        print(f"   目标：FP32精度，与PyTorch模型保持一致")

        try:
            # 1. 构建所有引擎
            build_results, engine_paths = self.build_all_engines()

            if not engine_paths:
                print(f"\n❌ 没有成功构建任何引擎")
                return False

            # 2. 验证所有引擎
            validation_results = self.validate_all_engines(engine_paths)

            # 3. 验证与PyTorch的一致性
            pytorch_consistency = self.validate_vs_pytorch(engine_paths)

            # 4. 总结结果
            print(f"\n{'='*60}")
            print(f"🎯 构建和验证结果总结:")

            successful_builds = sum(build_results.values())
            successful_validations = sum(1 for v in validation_results.values() if v['overall'])

            print(f"   引擎构建: {successful_builds}/{len(self.model_configs)} 成功")
            print(f"   引擎验证: {successful_validations}/{len(engine_paths)} 通过")
            print(f"   PyTorch一致性: {'✅ 通过' if pytorch_consistency else '❌ 失败'}")

            overall_success = (successful_builds == len(self.model_configs) and
                             successful_validations == len(engine_paths) and
                             pytorch_consistency)

            if overall_success:
                print(f"\n🎉 所有测试完全通过！")
                print(f"   新的FP32 TensorRT引擎已准备就绪")
                print(f"   引擎目录: {self.engine_dir}")
                print(f"   可以替换原有的TensorRT引擎使用")
            elif successful_builds > 0:
                print(f"\n✅ 部分成功！")
                print(f"   至少有 {successful_builds} 个引擎构建成功")
                print(f"   引擎目录: {self.engine_dir}")
            else:
                print(f"\n❌ 构建失败")

            return overall_success

        except Exception as e:
            print(f"❌ 工作流执行失败: {e}")
            traceback.print_exc()
            return False


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='构建TensorRT引擎')
    parser.add_argument('--precision', type=str, choices=['fp32', 'fp16'], default='fp32',
                        help='模型精度选择: fp32 或 fp16 (默认: fp32)')
    parser.add_argument('--checkpoint', type=str,
                        default="/data/outputs/2025.07.08/03.35.29_entangle_line_threefork_ruyiGAN_resnet50_entangle_line_threefork/checkpoints/epoch=0000-train_loss=0.024.ckpt",
                        help='PyTorch checkpoint路径')
    parser.add_argument('--onnx-dir', type=str, default="correct_onnx_models",
                        help='ONNX模型目录')
    parser.add_argument('--engine-dir', type=str, default="correct_trt_engines_working_method",
                        help='TensorRT引擎输出目录')

    args = parser.parse_args()

    print(f"🔧 构建配置:")
    print(f"   精度: {args.precision}")
    print(f"   Checkpoint: {args.checkpoint}")
    print(f"   ONNX目录: {args.onnx_dir}")
    print(f"   引擎目录: {args.engine_dir}")

    # 检查必要文件
    if not os.path.exists(args.checkpoint):
        print(f"❌ Checkpoint文件不存在: {args.checkpoint}")
        return False

    if not os.path.exists(args.onnx_dir):
        print(f"❌ ONNX模型目录不存在: {args.onnx_dir}")
        print(f"   请先运行 export_correct_onnx.py 生成正确的ONNX模型")
        return False

    # 检查ONNX文件
    required_files = ['obs_encoder_complete.onnx', 'unet_complete.onnx']
    for file in required_files:
        file_path = os.path.join(args.onnx_dir, file)
        if not os.path.exists(file_path):
            print(f"❌ ONNX文件不存在: {file_path}")
            return False
        else:
            file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
            print(f"✅ 找到 {file}: {file_size:.2f}MB")

    try:
        # 创建构建器
        builder = CorrectTensorRTBuilderUsingWorkingMethod(
            onnx_dir=args.onnx_dir,
            engine_dir=args.engine_dir,
            checkpoint_path=args.checkpoint,
            device='cuda',
            precision=args.precision
        )

        # 运行完整工作流
        success = builder.run_complete_workflow()

        return success

    except Exception as e:
        print(f"❌ 主函数执行失败: {e}")
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    print(f"\n{'='*60}")
    if success:
        print(f"🎉 使用已验证方法的TensorRT引擎构建成功！")
    else:
        print(f"❌ TensorRT引擎构建失败")

    sys.exit(0 if success else 1)
