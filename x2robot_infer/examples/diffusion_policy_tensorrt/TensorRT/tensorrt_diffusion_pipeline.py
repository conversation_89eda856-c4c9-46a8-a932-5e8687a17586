#!/usr/bin/env python3
"""
TensorRT Optimized Diffusion Policy Pipeline

This module provides a complete TensorRT-accelerated inference pipeline for
diffusion policy models, supporting DDIM 10-step inference with significant
speedup over standard PyTorch implementation.

Features:
- TensorRT accelerated observation encoder
- TensorRT accelerated UNet diffusion model  
- DDIM scheduler with configurable inference steps (default: 10)
- Automatic fallback to PyTorch if TensorRT engines fail
- Performance profiling and benchmarking
- CUDA graph optimization support
- Memory-efficient inference
"""

import os
import sys
import torch
import numpy as np
import time
from pathlib import Path
import json
from typing import Dict, List, Optional, Union
import traceback

# Optional imports for performance profiling
try:
    from cuda import cudart
    CUDA_AVAILABLE = True
except ImportError:
    CUDA_AVAILABLE = False
    cudart = None

try:
    import nvtx
    NVTX_AVAILABLE = True
except ImportError:
    NVTX_AVAILABLE = False
    nvtx = None

# Import diffusion policy components
from diffusion_policy.policy.diffusion_unet_image_policy import DiffusionUnetImagePolicy
from diffusion_policy.model.common.normalizer import LinearNormalizer
from diffusion_policy.common.pytorch_util import dict_apply

# Import TensorRT components
try:
    import tensorrt as trt
    TENSORRT_AVAILABLE = True
except ImportError:
    TENSORRT_AVAILABLE = False
    print("Warning: TensorRT not available. PyTorch fallback will be used.")

# Import DDIM scheduler
try:
    from diffusion_policy.policy.schedulers import DDIMTEDiScheduler
    DDIM_AVAILABLE = True
except ImportError:
    try:
        from diffusers import DDIMScheduler
        DDIM_AVAILABLE = True
    except ImportError:
        DDIM_AVAILABLE = False
        print("Warning: DDIM scheduler not available.")

def torch_dtype_from_trt(dtype):
    """Convert TensorRT dtype to PyTorch dtype."""
    if dtype == trt.float32:
        return torch.float32
    elif dtype == trt.float16:
        return torch.float16
    elif dtype == trt.int32:
        return torch.int32
    elif dtype == trt.int8:
        return torch.int8
    else:
        return torch.float32

class TensorRTEngine:
    """TensorRT engine wrapper."""
    
    def __init__(self, engine_path, device='cuda'):
        """Initialize TensorRT engine."""
        self.engine_path = engine_path
        self.device = device
        self.engine = None
        self.context = None
        self.buffers = None
        self.bindings = None
        self.stream = None
        self.is_tensorrt10 = False
        
        # 初始化引擎
        self.engine, self.context, self.buffers = self.initialize_engine(engine_path)
        
        if self.engine is None or self.context is None:
            raise RuntimeError(f"Failed to initialize TensorRT engine from {engine_path}")
    
    def initialize_engine(self, engine_path):
        """Initialize TensorRT engine."""
        try:
            # 创建logger
            logger = trt.Logger(trt.Logger.WARNING)
            
            # 加载引擎
            with open(engine_path, 'rb') as f:
                runtime = trt.Runtime(logger)
                engine = runtime.deserialize_cuda_engine(f.read())
            
            if engine is None:
                raise RuntimeError(f"Failed to load TensorRT engine from {engine_path}")
            
            # 创建执行上下文
            context = engine.create_execution_context()
            if context is None:
                raise RuntimeError(f"Failed to create TensorRT execution context for {engine_path}")
            
            # 检测TensorRT版本
            self.is_tensorrt10 = hasattr(engine, 'num_io_tensors')
            
            # 初始化缓冲区
            buffers = {}
            
            # 使用TensorRT 10.x API
            if self.is_tensorrt10:
                print(f"使用TensorRT 10.x API初始化引擎: {os.path.basename(engine_path)}")
                
                for i in range(engine.num_io_tensors):
                    name = engine.get_tensor_name(i)
                    mode = engine.get_tensor_mode(name)
                    dtype = engine.get_tensor_dtype(name)
                    shape = engine.get_tensor_shape(name)
                    
                    is_input = (mode == trt.TensorIOMode.INPUT)
                    
                    # 转换shape为元组
                    if hasattr(shape, 'dim'):
                        shape_tuple = tuple(shape.dim)
                    else:
                        shape_tuple = tuple(shape)
                    
                    # 检查是否有动态维度
                    has_dynamic_dims = any(dim < 0 for dim in shape_tuple)
                    
                    # 处理动态维度 (用1替换-1)
                    shape_for_allocation = []
                    for dim in shape_tuple:
                        if dim < 0:
                            # 动态维度，使用默认值1
                            shape_for_allocation.append(1)
                        else:
                            shape_for_allocation.append(dim)
                    
                    # 分配设备内存
                    size = np.prod(shape_for_allocation)
                    _, ptr = cudart.cudaMalloc(size * 4)  # 假设float32 (4字节)
                    
                    buffers[name] = {
                        'shape': shape_tuple,
                        'original_shape': shape,  # 保存原始形状
                        'size': size,
                        'ptr': ptr,
                        'is_input': is_input,
                        'is_dynamic': has_dynamic_dims  # 是否有动态维度
                    }
                    
                    print(f"  - 张量: {name}, 形状: {shape_tuple}, 输入: {is_input}, 动态: {buffers[name]['is_dynamic']}")
                    
                    # 如果是输入，设置形状
                    if is_input and has_dynamic_dims and hasattr(context, 'set_input_shape'):
                        # 为动态输入设置一个默认形状
                        default_shape = []
                        for dim in shape_tuple:
                            if dim < 0:
                                default_shape.append(1)  # 默认批量大小为1
                            else:
                                default_shape.append(dim)
                        
                        context.set_input_shape(name, default_shape)
                        print(f"    设置动态输入默认形状: {default_shape}")
            
            # 使用TensorRT 8.x API
            else:
                print(f"使用TensorRT 8.x API初始化引擎: {os.path.basename(engine_path)}")
                
                # 创建绑定缓冲区
                bindings = [None] * engine.num_bindings
                
                for i in range(engine.num_bindings):
                    name = engine.get_binding_name(i)
                    is_input = engine.binding_is_input(i)
                    dtype = engine.get_binding_dtype(i)
                    shape = engine.get_binding_shape(i)
                    
                    # 转换shape为元组
                    shape_tuple = tuple(shape)
                    
                    # 检查是否有动态维度
                    has_dynamic_dims = any(dim < 0 for dim in shape_tuple)
                    
                    # 处理动态维度 (用1替换-1)
                    shape_for_allocation = []
                    for dim in shape_tuple:
                        if dim < 0:
                            # 动态维度，使用默认值1
                            shape_for_allocation.append(1)
                        else:
                            shape_for_allocation.append(dim)
                    
                    # 分配设备内存
                    size = np.prod(shape_for_allocation)
                    _, ptr = cudart.cudaMalloc(size * 4)  # 假设float32 (4字节)
                    
                    # 存储绑定信息
                    bindings[i] = ptr
                    
                    buffers[name] = {
                        'shape': shape_tuple,
                        'original_shape': shape,  # 保存原始形状
                        'size': size,
                        'ptr': ptr,
                        'is_input': is_input,
                        'binding_index': i,
                        'is_dynamic': has_dynamic_dims  # 是否有动态维度
                    }
                    
                    print(f"  - 绑定 {i}: {name}, 形状: {shape_tuple}, 输入: {is_input}, 动态: {buffers[name]['is_dynamic']}")
                    
                    # 如果是动态形状的输入，设置默认形状
                    if is_input and has_dynamic_dims and hasattr(context, 'set_binding_shape'):
                        default_shape = []
                        for dim in shape_tuple:
                            if dim < 0:
                                default_shape.append(1)  # 默认批量大小为1
                            else:
                                default_shape.append(dim)
                        
                        context.set_binding_shape(i, default_shape)
                        print(f"    设置动态输入默认形状: {default_shape}")
                
                # 存储绑定数组
                self.bindings = bindings
            
            return engine, context, buffers
            
        except Exception as e:
            print(f"Error initializing TensorRT engine: {e}")
            import traceback
            traceback.print_exc()
            return None, None, None
            
    def allocate_buffers(self, batch_size=1):
        """Allocate GPU buffers for inputs and outputs."""
        try:
            import tensorrt as trt
            import numpy as np
            from cuda import cudart
            
            # 获取引擎的绑定数量
            num_bindings = self.engine.num_io_tensors if hasattr(self.engine, 'num_io_tensors') else self.engine.num_bindings
            
            # 清除现有的缓冲区
            self.buffers = {}
            self.bindings = []
            
            # 为每个绑定分配内存
            for i in range(num_bindings):
                # 获取绑定名称
                name = self.engine.get_tensor_name(i) if hasattr(self.engine, 'get_tensor_name') else self.engine.get_binding_name(i)
                
                # 获取绑定的数据类型
                dtype = self.engine.get_tensor_dtype(name) if hasattr(self.engine, 'get_tensor_dtype') else self.engine.get_binding_dtype(i)
                
                # 获取绑定的形状
                shape = self.engine.get_tensor_shape(name) if hasattr(self.engine, 'get_tensor_shape') else self.engine.get_binding_shape(i)
                
                # 处理动态批量大小
                if shape[0] == -1:
                    shape = (batch_size,) + tuple(shape[1:])
                
                # 计算大小（以字节为单位）
                size = np.dtype(trt.nptype(dtype)).itemsize
                for s in shape:
                    size *= s
                
                # 确保内存对齐到512字节边界
                alignment = 512
                size = (size + alignment - 1) // alignment * alignment
                
                # 分配设备内存
                device_ptr = cudart.cudaMalloc(size)[1]
                
                # 记录绑定
                self.bindings.append(int(device_ptr))
                
                # 存储缓冲区信息
                self.buffers[name] = {
                    'index': i,
                    'dtype': dtype,
                    'shape': shape,
                    'size': size,
                    'device_ptr': device_ptr,
                    'is_input': self.engine.get_tensor_mode(name) == trt.TensorIOMode.INPUT if hasattr(self.engine, 'get_tensor_mode') else self.engine.binding_is_input(i)
                }
                
                # 记录输出形状
                if not self.buffers[name]['is_input']:
                    self.output_shapes[name] = shape
            
            return True
        except Exception as e:
            print(f"分配缓冲区失败: {e}")
            return False
            
    def cleanup_buffers(self):
        """释放GPU缓冲区"""
        try:
            from cuda import cudart
            
            # 释放所有分配的GPU内存
            if hasattr(self, 'buffers') and self.buffers is not None:
                for name, buffer in self.buffers.items():
                    if 'device_ptr' in buffer and buffer['device_ptr']:
                        # 确保指针是有效的
                        if buffer['device_ptr'] != 0:
                            try:
                                cudart.cudaFree(buffer['device_ptr'])
                            except Exception as e:
                                print(f"释放缓冲区 {name} 失败: {e}")
            
            # 清空缓冲区和绑定
            self.buffers = {}
            if hasattr(self, 'bindings'):
                self.bindings = []
            
            return True
        except Exception as e:
            print(f"清理缓冲区失败: {e}")
            return False
            
    def _load_engine(self):
        """Load TensorRT engine."""
        try:
            import tensorrt as trt
            from cuda import cudart
            
            # 创建logger
            logger = trt.Logger(trt.Logger.WARNING)
            
            # 加载引擎
            print(f"Loading TensorRT engine: {self.engine_path}")
            with open(self.engine_path, 'rb') as f:
                runtime = trt.Runtime(logger)
                self.engine = runtime.deserialize_cuda_engine(f.read())
                
            if self.engine is None:
                print(f"Failed to load engine: {self.engine_path}")
                return False
            
            # 创建执行上下文
            self.context = self.engine.create_execution_context()
            if self.context is None:
                print(f"Failed to create context for engine: {self.engine_path}")
                return False
                
            # 创建CUDA流
            self.stream = cudart.cudaStreamCreate()[1]
            
            # 获取输入输出信息
            if hasattr(self.engine, 'num_io_tensors'):
                # TensorRT 10.x API
                for i in range(self.engine.num_io_tensors):
                    name = self.engine.get_tensor_name(i)
                    mode = self.engine.get_tensor_mode(name)
                    dtype = self.engine.get_tensor_dtype(name)
                    shape = self.engine.get_tensor_shape(name)
                    
                    # 存储信息
                    self.buffers[name] = {
                        'ptr': 0,  # 稍后分配
                        'shape': shape,
                        'dtype': dtype,
                        'is_input': mode == trt.TensorIOMode.INPUT
                    }
                    
                    # 存储输出形状
                    if mode != trt.TensorIOMode.INPUT:
                        self.output_shapes[name] = shape
            else:
                # TensorRT 8.x API
                for i in range(self.engine.num_bindings):
                    name = self.engine.get_binding_name(i)
                    is_input = self.engine.binding_is_input(i)
                    dtype = self.engine.get_binding_dtype(i)
                    shape = self.engine.get_binding_shape(i)
                    
                    # 存储信息
                    self.buffers[name] = {
                        'ptr': 0,  # 稍后分配
                        'shape': shape,
                        'dtype': dtype,
                        'is_input': is_input
                    }
                    
                    # 存储输出形状
                    if not is_input:
                        self.output_shapes[name] = shape
            
            # 分配缓冲区
            self.allocate_buffers(batch_size=1)
            
            self.is_initialized = True
            print(f"Engine loaded successfully: {self.engine_path}")
            return True
            
        except Exception as e:
            print(f"Error loading engine: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    def _setup_bindings(self):
        """Setup input/output bindings and shapes."""
        self.input_names = []
        self.output_names = []
        self.input_shapes = {}
        self.output_shapes = {}
        self.dtype_map = {}
        
        # Handle both TensorRT 8.x and 10.x APIs
        try:
            # TensorRT 10.x API
            num_tensors = self.engine.num_io_tensors
            for i in range(num_tensors):
                name = self.engine.get_tensor_name(i)
                dtype = trt.nptype(self.engine.get_tensor_dtype(name))
                shape = self.engine.get_tensor_shape(name)
                mode = self.engine.get_tensor_mode(name)
                
                self.dtype_map[name] = dtype
                
                # 处理动态形状，记录是否有动态批次维度
                shape_list = list(shape) if isinstance(shape, tuple) else list(shape.dim if hasattr(shape, 'dim') else shape)
                has_dynamic_batch = shape_list[0] == -1 if len(shape_list) > 0 else False
                
                if mode == trt.TensorIOMode.INPUT:
                    self.input_names.append(name)
                    self.input_shapes[name] = shape
                    if has_dynamic_batch:
                        print(f"输入 {name} 具有动态批次维度: {shape}")
                else:
                    self.output_names.append(name)
                    self.output_shapes[name] = shape
                    if has_dynamic_batch:
                        print(f"输出 {name} 具有动态批次维度: {shape}")
        except AttributeError:
            # TensorRT 8.x API
            num_bindings = self.engine.num_bindings
            for i in range(num_bindings):
                name = self.engine.get_binding_name(i)
                dtype = trt.nptype(self.engine.get_binding_dtype(i))
                shape = self.engine.get_binding_shape(i)
                
                self.dtype_map[name] = dtype
                
                # 处理动态形状，记录是否有动态批次维度
                shape_list = list(shape) if isinstance(shape, tuple) else list(shape)
                has_dynamic_batch = shape_list[0] == -1 if len(shape_list) > 0 else False
                
                if self.engine.binding_is_input(i):
                    self.input_names.append(name)
                    self.input_shapes[name] = shape
                    if has_dynamic_batch:
                        print(f"输入 {name} 具有动态批次维度: {shape}")
                else:
                    self.output_names.append(name)
                    self.output_shapes[name] = shape
                    if has_dynamic_batch:
                        print(f"输出 {name} 具有动态批次维度: {shape}")
                
    def infer(self, inputs: Dict[str, Union[torch.Tensor, np.ndarray]]) -> Dict[str, torch.Tensor]:
        """Run inference on the TensorRT engine."""
        try:
            # 获取CUDA流
            stream = torch.cuda.current_stream().cuda_stream
            
            # 首先为所有输出分配内存并设置地址
            outputs = {}
            output_tensors = {}
            
            # 获取批次大小
            batch_size = 1
            for name, tensor in inputs.items():
                if isinstance(tensor, torch.Tensor) and tensor.shape[0] > 0:
                    batch_size = tensor.shape[0]
                    break
                elif isinstance(tensor, np.ndarray) and tensor.shape[0] > 0:
                    batch_size = tensor.shape[0]
                    break
            
            # 为所有输出分配内存并设置地址
            for name, buffer in self.buffers.items():
                if not buffer['is_input']:
                    # 创建输出张量
                    shape = buffer['shape']
                    dtype = torch.float32  # 默认使用float32
                    
                    # 创建空的输出张量
                    if isinstance(shape, tuple) or isinstance(shape, list):
                        # 如果shape已经是元组或列表，处理动态维度
                        shape_tuple = list(shape)
                        # 替换动态批次维度 (-1) 为实际批次大小
                        if shape_tuple[0] == -1:
                            shape_tuple[0] = batch_size
                        output = torch.empty(shape_tuple, dtype=dtype, device=self.device)
                    else:
                        # 如果shape是TensorRT Dims对象或其他类型，尝试转换为元组
                        try:
                            if hasattr(shape, 'nbDims'):
                                # TensorRT 8.x API
                                shape_tuple = list(shape.d[i] for i in range(shape.nbDims))
                                # 替换动态批次维度
                                if shape_tuple[0] == -1:
                                    shape_tuple[0] = batch_size
                            elif hasattr(shape, 'dim'):
                                # TensorRT 10.x API
                                shape_tuple = list(shape.dim)
                                # 替换动态批次维度
                                if shape_tuple[0] == -1:
                                    shape_tuple[0] = batch_size
                            else:
                                # 尝试直接转换
                                shape_tuple = list(shape)
                                # 替换动态批次维度
                                if shape_tuple[0] == -1:
                                    shape_tuple[0] = batch_size
                            output = torch.empty(shape_tuple, dtype=dtype, device=self.device)
                        except Exception as e:
                            print(f"Error converting shape {shape} to tuple: {e}")
                            # 如果转换失败，尝试使用buffer的原始形状
                            if 'original_shape' in buffer:
                                original_shape = list(buffer['original_shape'])
                                # 替换动态批次维度
                                if original_shape[0] == -1:
                                    original_shape[0] = batch_size
                                output = torch.empty(original_shape, dtype=dtype, device=self.device)
                            else:
                                raise RuntimeError(f"Cannot create output tensor with shape {shape}")
                    
                    # 存储输出张量
                    output_tensors[name] = output
                    
                    # 为TensorRT 10.x设置输出张量地址
                    if hasattr(self.context, 'set_tensor_address'):
                        self.context.set_tensor_address(name, output.data_ptr())
                        # print(f"设置输出张量 {name} 的地址")
            
            # 处理每个输入
            for name, tensor in inputs.items():
                if name not in self.buffers:
                    continue
                
                buffer = self.buffers[name]
                if not buffer['is_input']:
                    continue
                
                # 将输入数据转换为numpy数组
                if isinstance(tensor, torch.Tensor):
                    # 确保张量在CUDA上
                    if tensor.device.type != 'cuda':
                        tensor = tensor.cuda()
                    # 转换为连续的内存布局
                    if not tensor.is_contiguous():
                        tensor = tensor.contiguous()
                    # 获取CUDA指针
                    ptr = tensor.data_ptr()
                else:
                    # 如果是numpy数组，复制到GPU
                    tensor = torch.from_numpy(tensor).cuda()
                    if not tensor.is_contiguous():
                        tensor = tensor.contiguous()
                    ptr = tensor.data_ptr()
                
                # 存储输入数据指针
                buffer['ptr'] = ptr
                
                # 为TensorRT 10.x设置张量地址和形状
                if hasattr(self.context, 'set_tensor_address'):
                    self.context.set_tensor_address(name, ptr)
                    
                    # 检查是否需要设置输入形状
                    if hasattr(self.context, 'set_input_shape') and hasattr(tensor, 'shape'):
                        # 获取当前形状
                        current_shape = tensor.shape
                        # 设置输入形状
                        try:
                            self.context.set_input_shape(name, current_shape)
                            # print(f"设置输入 {name} 的形状为 {current_shape}")
                        except Exception as shape_error:
                            print(f"设置输入形状失败: {shape_error}")
            
            # 执行推理
            if hasattr(self.context, 'execute_async_v3'):
                # print("使用 execute_async_v3 执行推理")
                if not self.context.execute_async_v3(stream):
                    raise RuntimeError("TensorRT execution failed")
            elif hasattr(self.context, 'execute_async_v2'):
                print("使用 execute_async_v2 执行推理")
                if not self.context.execute_async_v2(self.bindings, stream):
                    raise RuntimeError("TensorRT execution failed")
            else:
                raise RuntimeError("TensorRT context does not have execute_async_v2 or execute_async_v3 method")
            
            # 同步流，确保所有操作完成
            torch.cuda.current_stream().synchronize()
            
            # 返回输出张量
            return output_tensors
            
        except Exception as e:
            print(f"TensorRT inference failed: {e}")
            import traceback
            traceback.print_exc()
            
            # 不再回退到PyTorch策略，而是直接抛出异常
            raise RuntimeError(f"TensorRT inference failed and cannot continue: {e}")
            
    def cleanup(self):
        """Clean up TensorRT resources."""
        try:
            # 释放CUDA内存
            if hasattr(self, 'buffers') and self.buffers is not None:
                for name, buffer in self.buffers.items():
                    if 'ptr' in buffer and buffer['ptr'] is not None:
                        cudart.cudaFree(buffer['ptr'])
            
            # 清理其他资源
            self.engine = None
            self.context = None
            self.buffers = None
            self.bindings = None
            
        except Exception as e:
            print(f"清理缓冲区失败: {e}")
            import traceback
            traceback.print_exc()

    def __del__(self):
        """析构函数，确保资源被正确释放"""
        try:
            self.cleanup_buffers()
            
            # 释放CUDA图资源
            if hasattr(self, 'cuda_graph') and self.cuda_graph is not None:
                from cuda import cudart
                try:
                    cudart.cudaGraphDestroy(self.cuda_graph)
                except:
                    pass
                self.cuda_graph = None
                self.cuda_graph_exec = None
            
            # 释放CUDA流
            if hasattr(self, 'stream') and self.stream is not None:
                from cuda import cudart
                try:
                    cudart.cudaStreamDestroy(self.stream)
                except:
                    pass
                self.stream = None
            
            # 释放执行上下文
            if hasattr(self, 'context') and self.context is not None:
                self.context = None
            
            # 释放引擎
            if hasattr(self, 'engine') and self.engine is not None:
                self.engine = None
            
            # 强制垃圾回收
            import gc
            gc.collect()
        except Exception as e:
            print(f"清理TensorRT引擎资源失败: {e}")
            pass


class TensorRTDiffusionPipeline:
    """TensorRT accelerated diffusion policy pipeline."""
    
    def __init__(
        self,
                 engine_dir: str = "test_trt_engines",
        checkpoint_path: Optional[str] = None,
                 max_batch_size: int = 16,
        num_inference_steps: int = 100,
        device: str = "cuda",
        verbose: bool = False
    ):
        """
        Initialize TensorRT accelerated Diffusion Policy pipeline.
        
        Args:
            engine_dir: Directory containing TensorRT engines
            checkpoint_path: Path to PyTorch checkpoint for comparison
            max_batch_size: Maximum batch size
            num_inference_steps: Number of DDIM inference steps
            device: Device to run inference on
            verbose: Whether to print verbose output
        """
        self.engine_dir = engine_dir
        self.max_batch_size = max_batch_size
        self.num_inference_steps = num_inference_steps
        self.device = device
        self.verbose = verbose
        self.nvtx_profile = False
        self.markers = {}
        
        # Check CUDA availability
        if not torch.cuda.is_available():
            raise RuntimeError("CUDA is not available. Cannot run TensorRT pipeline.")
        
        # Initialize engines
        self.engines = {}
        
        # Initialize events for timing
        self.events = {}
        
        # Initialize stream
        self.stream = None
        if CUDA_AVAILABLE:
            from cuda import cudart
            self.stream = cudart.cudaStreamCreate()[1]
        
        # Cache for step results to avoid redundant computation
        self.step_cache = {}
        
        # Load engines
        self._load_engines()
        
        # Load PyTorch policy for comparison
        self.pytorch_policy = self._load_pytorch_policy(checkpoint_path)
        
        # Create DDIM scheduler
        self.scheduler = self._create_scheduler()
        
        # Normalizer
        self.normalizer = None
        if self.pytorch_policy is not None:
            self.normalizer = self.pytorch_policy.normalizer
            # 确保normalizer包含所有必要的参数
            self._setup_normalizer()
        else:
            # 创建默认normalizer
            self._create_default_normalizer()
            
        # Warmup engines
        self._warmup_engines()
        
        # Track if this is the first inference
        self._first_inference = True
        
    def _setup_normalizer(self):
        """Setup normalizer with all necessary parameters."""
        if self.normalizer is None or self.pytorch_policy is None:
            return
            
        try:
            # 确保normalizer包含图像观测的normalizer参数
            # 图像观测通常不需要normalization，但需要占位符
            from diffusion_policy.model.common.normalizer import SingleFieldLinearNormalizer
            
            # 检查并添加缺失的图像观测normalizer
            image_keys = ['face_view', 'left_wrist_view', 'right_wrist_view']
            for key in image_keys:
                # 直接检查params_dict中是否存在
                if key not in self.normalizer.params_dict:
                    # 为图像观测创建identity normalizer
                    self.normalizer.params_dict[key] = SingleFieldLinearNormalizer.create_identity().params_dict
                    
            # 确保agent_pos有normalizer
            if 'agent_pos' not in self.normalizer.params_dict:
                action_dim = self.pytorch_policy.action_dim if self.pytorch_policy else 14
                self.normalizer.params_dict["agent_pos"] = SingleFieldLinearNormalizer.create_bi_arm_identity(action_dim=action_dim).params_dict
                
            # 确保action有normalizer
            if 'action' not in self.normalizer.params_dict:
                action_dim = self.pytorch_policy.action_dim if self.pytorch_policy else 14
                self.normalizer.params_dict["action"] = SingleFieldLinearNormalizer.create_bi_arm_identity(action_dim=action_dim).params_dict
                
            if self.verbose:
                # 安全地获取normalizer的keys
                try:
                    keys = list(self.normalizer.params_dict.keys())
                    print(f"Normalizer setup complete. Keys: {keys}")
                except:
                    print("Normalizer setup complete.")
                
        except Exception as e:
            print(f"Error setting up normalizer: {e}")
            import traceback
            traceback.print_exc()
            
    def _create_ddim_scheduler(self):
        """Create DDIM scheduler with proper configuration."""
        try:
            # 使用与PyTorch策略相同的DDIMScheduler
            if self.verbose:
                print("Creating DDIM scheduler to match PyTorch policy...")
            
            # 从PyTorch策略中获取调度器配置
            if self.pytorch_policy is not None and hasattr(self.pytorch_policy, 'noise_scheduler'):
                # 直接使用PyTorch策略的调度器
                scheduler = self.pytorch_policy.noise_scheduler
                # 关键：为推理设置时间步数
                scheduler.set_timesteps(num_inference_steps=20)
                if self.verbose:
                    print(f"✓ Using PyTorch policy's noise scheduler: {type(scheduler)}")
                return scheduler
            
            # 如果PyTorch策略没有调度器，创建一个与教师模型相同的调度器
            try:
                from diffusers import DDIMScheduler
                scheduler = DDIMScheduler(
                    num_train_timesteps=100,
                    beta_start=0.0001,
                    beta_end=0.02,
                    beta_schedule="squaredcos_cap_v2",
                    clip_sample=True,
                    set_alpha_to_one=True,
                    steps_offset=0,
                    prediction_type="epsilon"
                )
                
                # 设置推理时间步数
                scheduler.set_timesteps(num_inference_steps=20)
                
                if self.verbose:
                    print("✓ Created DDIMScheduler with teacher model configuration")
                return scheduler
                
            except ImportError:
                print("diffusers not available, trying custom scheduler...")
            
            # 备选方案：使用自定义调度器
            try:
                from diffusion_policy.policy.schedulers import DDIMTEDiScheduler
                scheduler = DDIMTEDiScheduler(
                    num_train_timesteps=100,
                    beta_start=0.0001,
                    beta_end=0.02,
                    beta_schedule="squaredcos_cap_v2",
                    clip_sample=True,
                    set_alpha_to_one=True,
                    steps_offset=0,
                    prediction_type="epsilon"
                )
                
                # 设置推理时间步数
                scheduler.set_timesteps(num_inference_steps=20)
                
                if self.verbose:
                    print("✓ Created DDIMTEDiScheduler")
                return scheduler
                
            except ImportError as e:
                print(f"Custom scheduler import failed: {e}")
            
            # 最后的备选方案：创建简单调度器
            print("Creating simple fallback scheduler...")
            class SimpleScheduler:
                def __init__(self, num_steps=20):
                    self.timesteps = list(range(num_steps-1, -1, -1))
                    
                def step(self, noise_pred, timestep, sample):
                    # 简单的线性插值
                    alpha = 0.9
                    return type('obj', (object,), {
                        'prev_sample': alpha * sample + (1 - alpha) * noise_pred
                    })()
            
            scheduler = SimpleScheduler(num_steps=20)
            if self.verbose:
                print("✓ Created simple fallback scheduler")
            return scheduler
            
        except Exception as e:
            print(f"Error creating DDIM scheduler: {e}")
            return None
        
    def _load_engines(self):
        """加载TensorRT引擎"""
        try:
            # 检查引擎目录是否存在
            if not os.path.exists(self.engine_dir):
                raise RuntimeError(f"Engine directory not found: {self.engine_dir}")
            
            # 加载观测编码器引擎
            obs_engine_path = os.path.join(self.engine_dir, "obs_encoder.trt")
            if os.path.exists(obs_engine_path):
                self.engines['obs_encoder'] = TensorRTEngine(obs_engine_path, device=self.device)
                print(f"TensorRT engine loaded: {obs_engine_path}")
            else:
                print(f"Warning: Observation encoder engine not found at {obs_engine_path}")
            
            # 加载UNet引擎
            unet_engine_path = os.path.join(self.engine_dir, "unet.trt")
            if os.path.exists(unet_engine_path):
                self.engines['unet'] = TensorRTEngine(unet_engine_path, device=self.device)
                print(f"TensorRT engine loaded: {unet_engine_path}")
            else:
                print(f"Warning: UNet engine not found at {unet_engine_path}")
            
            # 检查是否成功加载了引擎
            if not self.engines:
                raise RuntimeError(f"No TensorRT engines found in {self.engine_dir}")
                
            print(f"TensorRT engines loaded: {list(self.engines.keys())}")
            return True
            
        except Exception as e:
            print(f"Error loading TensorRT engines: {e}")
            import traceback
            traceback.print_exc()
            return False
                
    def _warmup_engines(self):
        """预热TensorRT引擎，避免首次推理时的延迟"""
        try:
            print("预热TensorRT引擎...")
            
            # 预热观测编码器
            if 'obs_encoder' in self.engines:
                try:
                    print("预热观测编码器...")
                    
                    # 创建一个小批量的假数据
                    dummy_obs = {
                        'face_view': torch.zeros((1, 1, 3, 480, 640), dtype=torch.float32, device=self.device),
                        'left_wrist_view': torch.zeros((1, 1, 3, 480, 640), dtype=torch.float32, device=self.device),
                        'right_wrist_view': torch.zeros((1, 1, 3, 480, 640), dtype=torch.float32, device=self.device),
                        'agent_pos': torch.zeros((1, 1, 14), dtype=torch.float32, device=self.device)
                    }
                    
                    # 使用假数据运行一次推理
                    _ = self._encode_observations(dummy_obs)
                    print("观测编码器预热完成")
                except Exception as e:
                    print(f"观测编码器预热失败: {e}")
                    import traceback
                    traceback.print_exc()
            
            # 预热UNet
            if 'unet' in self.engines:
                try:
                    print("预热UNet...")
                    
                    # 创建一个小批量的假数据
                    batch_size = 1
                    action_dim = 14
                    horizon = 20
                    
                    dummy_inputs = {
                        'sample': torch.zeros((batch_size, horizon, action_dim), dtype=torch.float32, device='cpu'),
                        'timestep': torch.zeros((batch_size,), dtype=torch.float32, device='cpu'),
                        'global_cond': torch.zeros((batch_size, 6158), dtype=torch.float32, device='cpu')
                    }
                    
                    # 使用假数据运行一次推理
                    _ = self.engines['unet'].infer(dummy_inputs)
                    print("UNet预热完成")
                except Exception as e:
                    print(f"UNet预热失败: {e}")
                    import traceback
                    traceback.print_exc()
            
        except Exception as e:
            print(f"引擎预热失败: {e}")
            import traceback
            traceback.print_exc()
                
    def load_resources(self, batch_size: int):
        """
        Load and allocate resources for inference.
        
        Args:
            batch_size (int): Batch size for inference
        """
        # Create CUDA events for profiling
        if (self.verbose or self.nvtx_profile) and CUDA_AVAILABLE:
            for stage in ['obs_encode', 'diffusion', 'total']:
                self.events[stage] = [
                    cudart.cudaEventCreate()[1], 
                    cudart.cudaEventCreate()[1]
                ]
                
        # Allocate TensorRT buffers
        for name, engine in self.engines.items():
            engine.allocate_buffers(batch_size)
            
    def predict_action(self, obs_dict, deterministic=True):
        """
        预测动作
        
        Args:
            obs_dict: 观测数据字典
            deterministic: 是否确定性采样
            
        Returns:
            预测的动作字典
        """
        try:
            # 获取批量大小
            batch_size = 1
            if isinstance(obs_dict, dict):
                for key, value in obs_dict.items():
                    if hasattr(value, 'shape') and len(value.shape) > 0:
                        batch_size = value.shape[0]
                        break
            
            # 编码观测数据
            obs_features = self._encode_observations(obs_dict)
            
            # 采样动作
            actions = self._sample_actions(obs_features, batch_size, deterministic)

            # 确保张量在正确的设备上
            if actions.device.type != 'cuda' and self.device == 'cuda':
                actions = actions.to(self.device)

            # 应用动作反归一化处理（关键修复）
            if self.normalizer is not None and 'action' in self.normalizer.params_dict:
                # 需要先将维度从 (batch, action_dim, horizon) 转回 (batch, horizon, action_dim)
                # 因为normalizer期望的是 (batch, horizon, action_dim) 格式
                if actions.shape == (batch_size, 14, 20):  # (batch, action_dim, horizon)
                    actions_for_unnorm = actions.transpose(1, 2)  # -> (batch, horizon, action_dim)
                else:
                    actions_for_unnorm = actions

                # 应用反归一化
                try:
                    actions_unnormalized = self.normalizer['action'].unnormalize(actions_for_unnorm)

                    # 转回原来的格式 (batch, action_dim, horizon)
                    if actions_unnormalized.shape == (batch_size, 20, 14):  # (batch, horizon, action_dim)
                        actions = actions_unnormalized.transpose(1, 2)  # -> (batch, action_dim, horizon)
                    else:
                        actions = actions_unnormalized

                except Exception as e:
                    if self.verbose:
                        print(f"Warning: Failed to apply action unnormalization: {e}")
                    # 如果反归一化失败，使用原始actions
                    pass
            else:
                if self.verbose:
                    print("Warning: No action normalizer found, skipping unnormalization")

            # 返回结果
            return {
                'action': actions,
                'features': obs_features
            }
            
        except Exception as e:
            print(f"TensorRT inference failed: {e}")
            import traceback
            traceback.print_exc()
            
            # 不再回退到PyTorch策略，而是直接抛出异常
            raise RuntimeError(f"TensorRT inference failed and cannot continue: {e}")
                
    def _encode_observations(self, obs_dict):
        """
        编码观测数据
        
        Args:
            obs_dict: 观测数据字典
            
        Returns:
            编码后的观测特征
        """
        try:
            # 准备输入
            inputs = {}
            
            # 处理图像观测
            for key, value in obs_dict.items():
                if key in ['face_view', 'left_wrist_view', 'right_wrist_view']:
                    # 确保图像数据格式正确
                    if isinstance(value, torch.Tensor):
                        # 如果是PyTorch张量，确保形状正确
                        if len(value.shape) == 5:  # [B, T, C, H, W]
                            # 只除以255，不应用其他归一化
                            inputs[key] = value / 255.0
                        elif len(value.shape) == 4:  # [B, C, H, W]
                            # 只除以255，不应用其他归一化
                            inputs[key] = (value / 255.0).unsqueeze(1)  # 添加时间维度
                        else:
                            raise ValueError(f"图像观测 {key} 形状错误: {value.shape}")
                    elif isinstance(value, np.ndarray):
                        # 如果是numpy数组，转换为PyTorch张量
                        if len(value.shape) == 5:  # [B, T, C, H, W]
                            # 只除以255，不应用其他归一化
                            inputs[key] = torch.from_numpy(value).to(self.device) / 255.0
                        elif len(value.shape) == 4:  # [B, C, H, W]
                            # 只除以255，不应用其他归一化
                            inputs[key] = (torch.from_numpy(value).to(self.device) / 255.0).unsqueeze(1)
                        else:
                            raise ValueError(f"图像观测 {key} 形状错误: {value.shape}")
                elif key == 'agent_pos':
                    # 处理低维观测
                    if isinstance(value, torch.Tensor):
                        if len(value.shape) == 3:  # [B, T, D]
                            processed_value = value
                        elif len(value.shape) == 2:  # [B, D]
                            processed_value = value.unsqueeze(1)  # 添加时间维度
                        else:
                            raise ValueError(f"低维观测 {key} 形状错误: {value.shape}")
                    elif isinstance(value, np.ndarray):
                        if len(value.shape) == 3:  # [B, T, D]
                            processed_value = torch.from_numpy(value).to(self.device)
                        elif len(value.shape) == 2:  # [B, D]
                            processed_value = torch.from_numpy(value).unsqueeze(1).to(self.device)
                        else:
                            raise ValueError(f"低维观测 {key} 形状错误: {value.shape}")
                    
                    # 应用normalizer
                    if self.normalizer is not None and key in self.normalizer.params_dict:
                        try:
                            inputs[key] = self.normalizer[key].normalize(processed_value)
                        except Exception as e:
                            if self.verbose:
                                print(f"Warning: Failed to apply normalizer to {key}: {e}")
                            inputs[key] = processed_value
                    else:
                        if self.verbose:
                            print(f"Warning: No normalizer found for {key}, using raw values")
                        inputs[key] = processed_value
            
            # 如果obs_dict是嵌套字典，处理obs键
            if 'obs' in obs_dict and isinstance(obs_dict['obs'], dict):
                for key, value in obs_dict['obs'].items():
                    if key in ['face_view', 'left_wrist_view', 'right_wrist_view']:
                        # 确保图像数据格式正确
                        if isinstance(value, torch.Tensor):
                            # 如果是PyTorch张量，确保形状正确
                            if len(value.shape) == 5:  # [B, T, C, H, W]
                                # 只除以255，不应用其他归一化
                                inputs[key] = value / 255.0
                            elif len(value.shape) == 4:  # [B, C, H, W]
                                # 只除以255，不应用其他归一化
                                inputs[key] = (value / 255.0).unsqueeze(1)  # 添加时间维度
                            else:
                                raise ValueError(f"图像观测 {key} 形状错误: {value.shape}")
                        elif isinstance(value, np.ndarray):
                            # 如果是numpy数组，转换为PyTorch张量
                            if len(value.shape) == 5:  # [B, T, C, H, W]
                                # 只除以255，不应用其他归一化
                                inputs[key] = torch.from_numpy(value).to(self.device) / 255.0
                            elif len(value.shape) == 4:  # [B, C, H, W]
                                # 只除以255，不应用其他归一化
                                inputs[key] = (torch.from_numpy(value).to(self.device) / 255.0).unsqueeze(1)
                            else:
                                raise ValueError(f"图像观测 {key} 形状错误: {value.shape}")
                    elif key == 'agent_pos':
                        # 处理低维观测
                        if isinstance(value, torch.Tensor):
                            if len(value.shape) == 3:  # [B, T, D]
                                processed_value = value
                            elif len(value.shape) == 2:  # [B, D]
                                processed_value = value.unsqueeze(1)  # 添加时间维度
                            else:
                                raise ValueError(f"低维观测 {key} 形状错误: {value.shape}")
                        elif isinstance(value, np.ndarray):
                            if len(value.shape) == 3:  # [B, T, D]
                                processed_value = torch.from_numpy(value).to(self.device)
                            elif len(value.shape) == 2:  # [B, D]
                                processed_value = torch.from_numpy(value).unsqueeze(1).to(self.device)
                            else:
                                raise ValueError(f"低维观测 {key} 形状错误: {value.shape}")
                        
                        # 应用normalizer
                        if self.normalizer is not None and key in self.normalizer.params_dict:
                            try:
                                inputs[key] = self.normalizer[key].normalize(processed_value)
                            except Exception as e:
                                if self.verbose:
                                    print(f"Warning: Failed to apply normalizer to {key}: {e}")
                                inputs[key] = processed_value
                        else:
                            if self.verbose:
                                print(f"Warning: No normalizer found for {key}, using raw values")
                            inputs[key] = processed_value
            
            # 使用TensorRT引擎进行编码
            if 'obs_encoder' in self.engines:
                outputs = self.engines['obs_encoder'].infer(inputs)
                
                # 检查输出是否为空
                if not outputs:
                    print("Warning: obs_encoder returned empty outputs")
                    # 不再使用PyTorch模型作为后备，而是直接抛出异常
                    raise RuntimeError("obs_encoder returned empty outputs")
                
                # 获取编码结果
                if len(outputs) > 0:
                    encoded_obs = outputs[list(outputs.keys())[0]]
                else:
                    raise RuntimeError("obs_encoder returned empty outputs")
                
                return encoded_obs
            elif self.pytorch_policy is not None:
                # 不再使用PyTorch模型作为后备，而是直接抛出异常
                raise RuntimeError("No TensorRT obs_encoder available")
            else:
                raise RuntimeError("No obs_encoder available")
                
        except Exception as e:
            print(f"编码观测数据失败: {e}")
            import traceback
            traceback.print_exc()
            
            # 不再使用PyTorch模型作为后备，而是直接抛出异常
            raise
        
    def _sample_actions(self, obs_features, batch_size=1, deterministic=False):
        """
        使用TensorRT引擎进行扩散采样
        
        Args:
            obs_features: 观测特征
            batch_size: 批量大小
            deterministic: 是否确定性采样
            
        Returns:
            采样的动作
        """
        try:
            # 检查UNet引擎是否存在
            if 'unet' not in self.engines:
                raise RuntimeError("UNet engine not found")
                
            # 获取模型参数
            action_dim = 14  # 默认动作维度
            horizon = 20     # 默认时间步长
            
            # 检查obs_features的批次大小，确保与提供的batch_size一致
            if isinstance(obs_features, torch.Tensor) and obs_features.shape[0] != batch_size:
                print(f"警告: obs_features的批次大小({obs_features.shape[0]})与提供的batch_size({batch_size})不一致")
                # 使用实际的批次大小
                batch_size = obs_features.shape[0]
            
            # 创建初始噪声 - 统一使用全零初始化以保持与PyTorch一致
            actions = torch.zeros((batch_size, horizon, action_dim), device=self.device)
            
            # 获取时间步
            timesteps = self.scheduler.timesteps
            
            # 执行扩散过程
            for i, t in enumerate(timesteps):
                # 准备时间步 - 确保使用正确的数据类型 (int64)
                timestep = torch.tensor([t], dtype=torch.int64, device='cpu')
                
                # 准备输入
                inputs = {
                    'sample': actions.cpu(),  # 确保在CPU上
                    'timestep': timestep,
                    'global_cond': obs_features.cpu()  # 确保在CPU上
                }
                
                # 运行TensorRT UNet
                outputs = self.engines['unet'].infer(inputs)
                
                # 检查输出是否为空
                if not outputs:
                    print(f"Warning: UNet inference returned empty outputs at step {i}")
                    # 使用零噪声作为后备
                    noise_pred = torch.zeros_like(actions)
                else:
                    # 获取噪声预测
                    if 'noise_pred' in outputs:
                        noise_pred = outputs['noise_pred']
                    elif len(outputs) > 0:
                        # 使用第一个输出
                        noise_pred = outputs[list(outputs.keys())[0]]
                    else:
                        # 使用零噪声作为后备
                        print(f"Warning: No valid outputs from UNet at step {i}")
                        noise_pred = torch.zeros_like(actions)
                
                # 更新样本
                scheduler_output = self.scheduler.step(
                    noise_pred, t, actions
                )
                actions = scheduler_output.prev_sample
                
                # 打印进度
                # if self.verbose and (i == 0 or i == len(timesteps) - 1 or (i + 1) % 20 == 0):
                #     print(f"  扩散步骤 {i+1}/{len(timesteps)}")
            
            # 转置结果
            actions = actions.transpose(1, 2)
            
            return actions
            
        except Exception as e:
            print(f"扩散采样失败: {e}")
            import traceback
            traceback.print_exc()
            
            # 返回一个全零张量作为后备
            return torch.zeros((batch_size, 14, 20), device=self.device)
        
    def _sample_actions_pytorch(self,
                               obs_features: torch.Tensor,
                               batch_size: int,
                               deterministic: bool = True) -> torch.Tensor:
        """Fallback PyTorch diffusion sampling."""
        if self.pytorch_policy is None:
            raise RuntimeError("No PyTorch policy for fallback")
            
        # Use PyTorch implementation
        action_dim = self.pytorch_policy.action_dim
        # Check for different horizon attribute names
        if hasattr(self.pytorch_policy, 'horizon'):
            horizon = self.pytorch_policy.horizon
        elif hasattr(self.pytorch_policy, 'action_horizon'):
            horizon = self.pytorch_policy.action_horizon
        else:
            horizon = 20  # Default fallback
        
        # PyTorch UNet expects [batch, action_dim, horizon] format
        shape = (batch_size, action_dim, horizon)
        if deterministic:
            actions = torch.zeros(shape, device=self.device)
        else:
            actions = torch.randn(shape, device=self.device)
            
        # Run PyTorch diffusion sampling
        if self.scheduler is not None:
            timesteps = self.scheduler.timesteps
        else:
            timesteps = self.pytorch_policy.noise_scheduler.timesteps
        
        for t in timesteps:
            timestep = torch.full((batch_size,), t, device=self.device, dtype=torch.long)
            
            # Predict noise - actions已经是正确的格式 [batch, action_dim, horizon]
            noise_pred = self.pytorch_policy.model(
                actions, timestep, global_cond=obs_features
            )
            
            # Update actions
            if self.scheduler is not None:
                scheduler_output = self.scheduler.step(
                    noise_pred, t, actions
                )
                actions = scheduler_output.prev_sample
            else:
                actions = self.pytorch_policy.noise_scheduler.step(
                    noise_pred, t, actions
                ).prev_sample
            
        return actions
        
    def _get_batch_size(self, obs_dict: Dict[str, torch.Tensor]) -> int:
        """Extract batch size from observation dictionary."""
        for value in obs_dict.values():
            if isinstance(value, torch.Tensor):
                return value.shape[0]
        return 1
        
    def profile_start(self, name: str, color: str = 'blue'):
        """Start profiling marker."""
        if hasattr(self, 'nvtx_profile') and self.nvtx_profile and NVTX_AVAILABLE:
            if not hasattr(self, 'markers'):
                self.markers = {}
            self.markers[name] = nvtx.start_range(message=name, color=color)
        if name in self.events and CUDA_AVAILABLE:
            cudart.cudaEventRecord(self.events[name][0], 0)
            
    def profile_stop(self, name: str):
        """Stop profiling marker."""
        if name in self.events and CUDA_AVAILABLE:
            cudart.cudaEventRecord(self.events[name][1], 0)
        if hasattr(self, 'nvtx_profile') and self.nvtx_profile and NVTX_AVAILABLE and hasattr(self, 'markers') and name in self.markers:
            nvtx.end_range(self.markers[name])
            
    def print_performance_summary(self, batch_size: int = 1):
        """Print performance summary."""
        if not self.events:
            print("No profiling data available")
            return
            
        print('|-----------------|--------------|')
        print('| {:^15} | {:^12} |'.format('Stage', 'Latency'))
        print('|-----------------|--------------|')
        
        total_time = 0
        for stage in ['obs_encode', 'diffusion']:
            if stage in self.events and CUDA_AVAILABLE:
                latency = cudart.cudaEventElapsedTime(
                    self.events[stage][0], self.events[stage][1]
                )[1]
                print('| {:^15} | {:>9.2f} ms |'.format(stage.title(), latency))
                total_time += latency
                
        print('|-----------------|--------------|')
        print('| {:^15} | {:>9.2f} ms |'.format('Total', total_time))
        print('|-----------------|--------------|')
        
        if total_time > 0:
            throughput = batch_size * 1000.0 / total_time
            print(f'Throughput: {throughput:.2f} actions/s')
            
    def benchmark(self,
                  batch_sizes: List[int] = [1, 4, 8, 16],
                  num_warmup: int = 10,
                  num_runs: int = 100) -> Dict[str, Dict[str, float]]:
        """
        Benchmark TensorRT vs PyTorch performance.
        
        Args:
            batch_sizes: List of batch sizes to test
            num_warmup: Number of warmup runs
            num_runs: Number of timing runs
            
        Returns:
            Performance comparison results
        """
        if self.pytorch_policy is None:
            print("No PyTorch policy available for comparison")
            return {}
            
        results = {}
        
        for batch_size in batch_sizes:
            print(f"\nBenchmarking batch size: {batch_size}")
            
            # Create dummy observations
            obs_dict = self._create_dummy_observations(batch_size)
            
            # Warmup
            for _ in range(num_warmup):
                _ = self.predict_action(obs_dict)
                
            # Benchmark TensorRT
            torch.cuda.synchronize()
            start_time = time.time()
            for _ in range(num_runs):
                _ = self.predict_action(obs_dict)
            torch.cuda.synchronize()
            trt_time = (time.time() - start_time) / num_runs * 1000  # ms
            
            # Benchmark PyTorch
            torch.cuda.synchronize()
            start_time = time.time()
            for _ in range(num_runs):
                _ = self.pytorch_policy.predict_action(obs_dict)
            torch.cuda.synchronize()
            pytorch_time = (time.time() - start_time) / num_runs * 1000  # ms
            
            speedup = pytorch_time / trt_time if trt_time > 0 else 0
            
            results[f'batch_{batch_size}'] = {
                'tensorrt_ms': trt_time,
                'pytorch_ms': pytorch_time,
                'speedup': speedup
            }
            
            print(f"TensorRT: {trt_time:.2f} ms")
            print(f"PyTorch: {pytorch_time:.2f} ms")
            print(f"Speedup: {speedup:.2f}x")
            
        return results
        
    def _create_dummy_observations(self, batch_size: int) -> Dict[str, torch.Tensor]:
        """Create dummy observations for benchmarking."""
        obs_dict = {}
        
        # Add image observations with time dimension
        if self.pytorch_policy and hasattr(self.pytorch_policy, 'rgb_keys'):
            for key in self.pytorch_policy.rgb_keys:
                obs_dict[key] = torch.randn(batch_size, 1, 3, 224, 224, device=self.device)
                
        # Add low-dim observations with time dimension
        if self.pytorch_policy and hasattr(self.pytorch_policy, 'lowdim_keys'):
            for key in self.pytorch_policy.lowdim_keys:
                obs_dict[key] = torch.randn(batch_size, 1, 14, device=self.device)
                
        # Return in the expected format for policy.predict_action
        return {'obs': obs_dict}
        
    def _create_scheduler(self):
        """Create DDIM scheduler."""
        try:
            from diffusers.schedulers import DDIMScheduler
            
            # Create scheduler with default parameters
            scheduler = DDIMScheduler(
                num_train_timesteps=1000,
                beta_start=0.0001,
                beta_end=0.02,
                beta_schedule="linear"
            )
            
            # Set timesteps for inference
            scheduler.set_timesteps(self.num_inference_steps, device=self.device)
            
            if self.verbose:
                print(f"DDIM scheduler created with {self.num_inference_steps} inference steps")
                
            return scheduler
            
        except Exception as e:
            print(f"Error creating DDIM scheduler: {e}")
            import traceback
            traceback.print_exc()
            
            # 创建简单的替代调度器
            class SimpleScheduler:
                def __init__(self, num_steps=20):
                    self.timesteps = torch.linspace(999, 0, num_steps).long()
                    self.num_inference_steps = num_steps
                    
                def step(self, noise_pred, timestep, sample):
                    # 简单的线性插值
                    alpha = 1.0 - (timestep / 1000.0)
                    prev_sample = sample - alpha * noise_pred
                    
                    # 创建一个类似于diffusers的输出对象
                    class Output:
                        def __init__(self, prev_sample):
                            self.prev_sample = prev_sample
                            
                    return Output(prev_sample)
                    
                def set_timesteps(self, num_inference_steps, device='cpu'):
                    self.timesteps = torch.linspace(999, 0, num_inference_steps).long().to(device)
                    self.num_inference_steps = num_inference_steps
            
            print("创建简单的替代调度器")
            return SimpleScheduler(num_steps=self.num_inference_steps)

    def _load_pytorch_policy(self, checkpoint_path=None):
        """Load PyTorch policy from checkpoint for comparison."""
        try:
            if checkpoint_path is None:
                print("No checkpoint provided, skipping PyTorch policy loading")
                return None
                
            print(f"尝试从checkpoint加载模型配置: {checkpoint_path}")
            
            # 加载checkpoint，设置weights_only=False以解决PyTorch 2.6+的加载问题
            ckpt = torch.load(checkpoint_path, map_location='cpu', weights_only=False)
            print(f"Checkpoint keys: {ckpt.keys()}")
            
            if 'cfg' in ckpt:
                # 使用checkpoint中的配置实例化策略
                print(f"使用checkpoint中的cfg.policy实例化policy")
                cfg = ckpt['cfg']
                
                # 检查是否有policy配置
                if hasattr(cfg, 'policy'):
                    # 使用shape_meta
                    if hasattr(cfg.policy, 'shape_meta'):
                        print(f"使用cfg.policy中的shape_meta")
                    
                    # 创建策略
                    import hydra
                    policy = hydra.utils.instantiate(cfg.policy)
                    
                    # 加载权重
                    print(f"从state_dicts['model']中提取权重")
                    state_dict = ckpt['state_dicts']['model']
                    
                    # 计数加载的参数
                    num_params = len(state_dict)
                    print(f"加载权重: {num_params} 个参数")
                    
                    # 加载权重
                    missing_keys, unexpected_keys = policy.load_state_dict(state_dict, strict=False)
                    
                    # 打印兼容性信息
                    num_compatible = num_params - len(missing_keys)
                    print(f"✅ 兼容参数: {num_compatible}")
                    print(f"⚠️ 不兼容参数: {len(missing_keys)}")
                    
                    if len(missing_keys) > 0:
                        print(f"缺失键: {missing_keys[:5]}...")
                    
                    if len(unexpected_keys) > 0:
                        print(f"意外键: {unexpected_keys[:5]}...")
                    
                    print(f"权重加载完成！")
                    
                    # 将策略移到GPU
                    policy = policy.to(self.device)
                    policy.eval()
                    
                    return policy
                else:
                    print(f"Checkpoint中没有policy配置")
                    return None
            else:
                print(f"Checkpoint中没有cfg键")
                return None
                
        except Exception as e:
            print(f"加载PyTorch策略时出错: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _create_default_normalizer(self):
        """创建默认的normalizer"""
        try:
            from diffusion_policy.model.common.normalizer import LinearNormalizer, SingleFieldLinearNormalizer
            
            print("创建默认normalizer")
            action_dim = 14  # 默认动作维度
            
            # 创建LinearNormalizer
            self.normalizer = LinearNormalizer()
            
            # 为action添加normalizer
            self.normalizer.params_dict["action"] = SingleFieldLinearNormalizer.create_bi_arm_identity(action_dim=action_dim).params_dict
            
            # 为agent_pos添加normalizer
            self.normalizer.params_dict["agent_pos"] = SingleFieldLinearNormalizer.create_bi_arm_identity(action_dim=action_dim).params_dict
            
            # 为图像观测添加normalizer
            image_keys = ['face_view', 'left_wrist_view', 'right_wrist_view']
            for key in image_keys:
                self.normalizer.params_dict[key] = SingleFieldLinearNormalizer.create_identity().params_dict
                
            if self.verbose:
                print(f"默认normalizer已创建，包含keys: {list(self.normalizer.params_dict.keys())}")
                
        except Exception as e:
            print(f"创建默认normalizer失败: {e}")
            import traceback
            traceback.print_exc()

    def teardown(self):
        """Clean up resources."""
        # Clean up CUDA events
        if CUDA_AVAILABLE:
            for events in self.events.values():
                if events:
                    cudart.cudaEventDestroy(events[0])
                    cudart.cudaEventDestroy(events[1])
                    
            # Clean up CUDA stream
            if self.stream:
                cudart.cudaStreamDestroy(self.stream)
            
        # Clean up TensorRT engines
        for engine in self.engines.values():
            engine.cleanup()
            
        self.events = {}
        self.stream = None
        self.engines = {}
        
    def __del__(self):
        """Destructor to ensure cleanup."""
        self.teardown()


def create_tensorrt_pipeline(
    policy: DiffusionUnetImagePolicy,
    engine_dir: str = "test_trt_engines",
    max_batch_size: int = 16,
    num_inference_steps: int = 10,
    **kwargs
) -> TensorRTDiffusionPipeline:
    """
    Create TensorRT accelerated Diffusion Policy pipeline.
    
    Args:
        policy: Original PyTorch policy
        engine_dir: Directory for TensorRT engines
        max_batch_size: Maximum batch size
        num_inference_steps: Number of DDIM inference steps
        **kwargs: Additional pipeline arguments
        
    Returns:
        TensorRT accelerated pipeline
    """
    pipeline = TensorRTDiffusionPipeline(
        policy=policy,
        engine_dir=engine_dir,
        max_batch_size=max_batch_size,
        num_inference_steps=num_inference_steps,
        **kwargs
    )
    
    return pipeline


if __name__ == "__main__":
    # Example usage
    print("TensorRT Diffusion Policy Pipeline")
    print("==================================")
    
    # This would be used with a trained policy
    # pipeline = create_tensorrt_pipeline(policy, num_inference_steps=10)
    # result = pipeline.predict_action(obs_dict)
    # pipeline.print_performance_summary()
    # pipeline.benchmark()
    # pipeline.teardown()
    
    print("Pipeline ready for use!") 