#!/usr/bin/env python3
"""
TensorRT性能分析工具
分析TensorRT推理性能瓶颈
"""

import torch
import numpy as np
import time
import traceback
from tensorrt_diffusion_pipeline import TensorRTDiffusionPipeline

def create_test_observations(batch_size=1):
    """创建测试观测数据"""
    return {
        'obs': {
            'face_view': torch.rand(batch_size, 1, 3, 480, 640, device='cuda'),
            'left_wrist_view': torch.rand(batch_size, 1, 3, 480, 640, device='cuda'),
            'right_wrist_view': torch.rand(batch_size, 1, 3, 480, 640, device='cuda'),
            'agent_pos': torch.rand(batch_size, 1, 14, device='cuda')
        }
    }

def benchmark_individual_components(pipeline, obs_dict, num_runs=10):
    """分别测试各个组件的性能"""
    
    print("🔍 组件级性能分析")
    print("=" * 60)
    
    results = {}
    
    # 1. 测试obs_encoder性能
    print("\n📊 测试obs_encoder性能...")
    
    # 准备obs_encoder输入
    obs_inputs = {
        'face_view': obs_dict['obs']['face_view'].cpu().numpy().astype(np.float32),
        'left_wrist_view': obs_dict['obs']['left_wrist_view'].cpu().numpy().astype(np.float32),
        'right_wrist_view': obs_dict['obs']['right_wrist_view'].cpu().numpy().astype(np.float32),
        'agent_pos': obs_dict['obs']['agent_pos'].cpu().numpy().astype(np.float32)
    }
    
    # 预热
    for _ in range(3):
        _ = pipeline.engines['obs_encoder'].infer(obs_inputs)
    
    # 测试obs_encoder
    torch.cuda.synchronize()
    start_time = time.perf_counter()
    for _ in range(num_runs):
        obs_features = pipeline.engines['obs_encoder'].infer(obs_inputs)
    torch.cuda.synchronize()
    obs_encoder_time = (time.perf_counter() - start_time) / num_runs * 1000
    
    print(f"   obs_encoder平均时间: {obs_encoder_time:.2f}ms")
    results['obs_encoder'] = obs_encoder_time
    
    # 2. 测试UNet性能
    print("\n📊 测试UNet性能...")
    
    # 获取obs_encoder输出
    obs_features_output = obs_features['encoded_obs']
    if isinstance(obs_features_output, torch.Tensor):
        obs_features_tensor = obs_features_output.to('cuda')
    else:
        obs_features_tensor = torch.from_numpy(obs_features_output).to('cuda')
    
    # 准备UNet输入
    batch_size = obs_features_tensor.shape[0]
    horizon = 20
    action_dim = 14
    
    # 创建噪声轨迹
    noisy_trajectory = torch.randn(batch_size, horizon, action_dim, device='cuda')
    
    # 测试单次UNet推理
    unet_times = []
    for i in range(num_runs):
        # 随机时间步
        timestep = torch.randint(0, 100, (batch_size,), device='cuda', dtype=torch.long)
        
        unet_inputs = {
            'sample': noisy_trajectory.cpu().numpy().astype(np.float32),
            'timestep': timestep.cpu().numpy().astype(np.int64),
            'global_cond': obs_features_tensor.cpu().numpy().astype(np.float32)
        }
        
        torch.cuda.synchronize()
        start_time = time.perf_counter()
        noise_pred = pipeline.engines['unet'].infer(unet_inputs)
        torch.cuda.synchronize()
        unet_time = (time.perf_counter() - start_time) * 1000
        
        unet_times.append(unet_time)
    
    avg_unet_time = np.mean(unet_times)
    print(f"   UNet平均时间: {avg_unet_time:.2f}ms")
    results['unet_single'] = avg_unet_time
    
    # 3. 测试完整扩散过程
    print("\n📊 测试完整扩散过程...")
    
    # 预热
    for _ in range(2):
        _ = pipeline.predict_action(obs_dict, deterministic=True)
    
    # 测试完整pipeline
    torch.cuda.synchronize()
    start_time = time.perf_counter()
    for _ in range(5):  # 扩散过程较慢，减少测试次数
        result = pipeline.predict_action(obs_dict, deterministic=True)
    torch.cuda.synchronize()
    full_pipeline_time = (time.perf_counter() - start_time) / 5 * 1000
    
    print(f"   完整pipeline平均时间: {full_pipeline_time:.2f}ms")
    results['full_pipeline'] = full_pipeline_time
    
    return results

def analyze_memory_transfer_overhead(pipeline, obs_dict):
    """分析内存传输开销"""
    
    print("\n🔍 内存传输开销分析")
    print("=" * 60)
    
    # 1. 测试CPU->GPU传输时间
    obs_cpu = {
        'face_view': obs_dict['obs']['face_view'].cpu().numpy().astype(np.float32),
        'left_wrist_view': obs_dict['obs']['left_wrist_view'].cpu().numpy().astype(np.float32),
        'right_wrist_view': obs_dict['obs']['right_wrist_view'].cpu().numpy().astype(np.float32),
        'agent_pos': obs_dict['obs']['agent_pos'].cpu().numpy().astype(np.float32)
    }
    
    # 计算数据大小
    total_size = 0
    for key, value in obs_cpu.items():
        size_mb = value.nbytes / (1024 * 1024)
        total_size += size_mb
        print(f"   {key}: {size_mb:.2f}MB")
    
    print(f"   总输入数据大小: {total_size:.2f}MB")
    
    # 测试内存传输时间
    num_runs = 10
    transfer_times = []
    
    for _ in range(num_runs):
        torch.cuda.synchronize()
        start_time = time.perf_counter()
        
        # 模拟数据传输到GPU
        gpu_data = {}
        for key, value in obs_cpu.items():
            gpu_data[key] = torch.from_numpy(value).to('cuda')
        
        torch.cuda.synchronize()
        transfer_time = (time.perf_counter() - start_time) * 1000
        transfer_times.append(transfer_time)
    
    avg_transfer_time = np.mean(transfer_times)
    print(f"   平均内存传输时间: {avg_transfer_time:.2f}ms")
    
    return avg_transfer_time

def analyze_synchronization_overhead(pipeline, obs_dict):
    """分析同步开销"""
    
    print("\n🔍 同步开销分析")
    print("=" * 60)
    
    # 准备输入
    obs_inputs = {
        'face_view': obs_dict['obs']['face_view'].cpu().numpy().astype(np.float32),
        'left_wrist_view': obs_dict['obs']['left_wrist_view'].cpu().numpy().astype(np.float32),
        'right_wrist_view': obs_dict['obs']['right_wrist_view'].cpu().numpy().astype(np.float32),
        'agent_pos': obs_dict['obs']['agent_pos'].cpu().numpy().astype(np.float32)
    }
    
    num_runs = 20
    
    # 1. 测试带同步的推理时间
    sync_times = []
    for _ in range(num_runs):
        torch.cuda.synchronize()
        start_time = time.perf_counter()
        _ = pipeline.engines['obs_encoder'].infer(obs_inputs)
        torch.cuda.synchronize()
        sync_time = (time.perf_counter() - start_time) * 1000
        sync_times.append(sync_time)
    
    avg_sync_time = np.mean(sync_times)
    
    # 2. 测试不带同步的推理时间（仅启动）
    async_times = []
    for _ in range(num_runs):
        start_time = time.perf_counter()
        _ = pipeline.engines['obs_encoder'].infer(obs_inputs)
        async_time = (time.perf_counter() - start_time) * 1000
        async_times.append(async_time)
    
    # 最后同步一次确保所有操作完成
    torch.cuda.synchronize()
    avg_async_time = np.mean(async_times)
    
    sync_overhead = avg_sync_time - avg_async_time
    
    print(f"   带同步推理时间: {avg_sync_time:.2f}ms")
    print(f"   异步推理时间: {avg_async_time:.2f}ms")
    print(f"   同步开销: {sync_overhead:.2f}ms ({sync_overhead/avg_sync_time*100:.1f}%)")
    
    return sync_overhead

def compare_with_pytorch_detailed(pipeline, obs_dict):
    """详细对比PyTorch性能"""
    
    print("\n🔍 详细PyTorch性能对比")
    print("=" * 60)
    
    num_runs = 10
    
    # 1. PyTorch obs_encoder性能
    print("\n📊 PyTorch obs_encoder性能...")
    
    # 准备PyTorch输入
    obs_data = obs_dict['obs']
    nobs = pipeline.pytorch_policy.normalizer.normalize(obs_data, ignore_keys=pipeline.pytorch_policy.rgb_keys)
    this_nobs = {k: v for k, v in nobs.items() if k != 'instruction'}
    
    # 预热
    for _ in range(3):
        with torch.no_grad():
            _ = pipeline.pytorch_policy.obs_encoder(this_nobs)
    
    # 测试PyTorch obs_encoder
    torch.cuda.synchronize()
    start_time = time.perf_counter()
    for _ in range(num_runs):
        with torch.no_grad():
            pytorch_encoded = pipeline.pytorch_policy.obs_encoder(this_nobs)
    torch.cuda.synchronize()
    pytorch_obs_time = (time.perf_counter() - start_time) / num_runs * 1000
    
    print(f"   PyTorch obs_encoder平均时间: {pytorch_obs_time:.2f}ms")
    
    # 2. PyTorch UNet性能
    print("\n📊 PyTorch UNet性能...")
    
    # 准备UNet输入
    batch_size = pytorch_encoded.shape[0]
    horizon = 20
    action_dim = 14
    
    noisy_trajectory = torch.randn(batch_size, horizon, action_dim, device='cuda')
    global_cond = pytorch_encoded.reshape(batch_size, -1)
    
    unet_times = []
    for _ in range(num_runs):
        timestep = torch.randint(0, 100, (batch_size,), device='cuda', dtype=torch.long)
        
        torch.cuda.synchronize()
        start_time = time.perf_counter()
        with torch.no_grad():
            # 检查PyTorch policy的实际属性
            if hasattr(pipeline.pytorch_policy, 'noise_pred_net'):
                noise_pred = pipeline.pytorch_policy.noise_pred_net(
                    noisy_trajectory, timestep, global_cond=global_cond
                )
            elif hasattr(pipeline.pytorch_policy, 'model'):
                noise_pred = pipeline.pytorch_policy.model(
                    noisy_trajectory, timestep, global_cond=global_cond
                )
            else:
                # 跳过UNet测试
                print("   ⚠️ 无法找到PyTorch UNet模型，跳过测试")
                return pytorch_obs_time, 0
        torch.cuda.synchronize()
        unet_time = (time.perf_counter() - start_time) * 1000
        unet_times.append(unet_time)
    
    pytorch_unet_time = np.mean(unet_times)
    print(f"   PyTorch UNet平均时间: {pytorch_unet_time:.2f}ms")
    
    return pytorch_obs_time, pytorch_unet_time

def main():
    """主函数"""
    
    print("🚀 TensorRT性能深度分析")
    print("=" * 80)
    
    # 初始化pipeline
    checkpoint_path = "/workspace/x2robot_infer/examples/diffusion_policy_tensorrt/TensorRT/epoch=0030-train_loss=0.005.ckpt"
    engine_dir = "correct_trt_engines_working_method"
    
    try:
        pipeline = TensorRTDiffusionPipeline(
            checkpoint_path=checkpoint_path,
            engine_dir=engine_dir,
            device='cuda',
            verbose=False
        )
        print("✅ Pipeline初始化成功")
    except Exception as e:
        print(f"❌ Pipeline初始化失败: {e}")
        return
    
    # 创建测试数据
    obs_dict = create_test_observations(batch_size=1)
    
    # 1. 组件级性能分析
    trt_results = benchmark_individual_components(pipeline, obs_dict)
    
    # 2. 内存传输开销分析
    transfer_overhead = analyze_memory_transfer_overhead(pipeline, obs_dict)
    
    # 3. 同步开销分析
    sync_overhead = analyze_synchronization_overhead(pipeline, obs_dict)
    
    # 4. PyTorch详细对比
    pytorch_obs_time, pytorch_unet_time = compare_with_pytorch_detailed(pipeline, obs_dict)
    
    # 5. 综合分析
    print("\n" + "=" * 80)
    print("📊 综合性能分析报告")
    print("=" * 80)
    
    print(f"\n🔧 TensorRT组件性能:")
    print(f"   obs_encoder: {trt_results['obs_encoder']:.2f}ms")
    print(f"   UNet (单次): {trt_results['unet_single']:.2f}ms")
    print(f"   完整pipeline: {trt_results['full_pipeline']:.2f}ms")
    
    print(f"\n🔧 PyTorch组件性能:")
    print(f"   obs_encoder: {pytorch_obs_time:.2f}ms")
    print(f"   UNet (单次): {pytorch_unet_time:.2f}ms")
    
    print(f"\n⚡ 组件加速比:")
    obs_speedup = pytorch_obs_time / trt_results['obs_encoder']
    unet_speedup = pytorch_unet_time / trt_results['unet_single']
    print(f"   obs_encoder: {obs_speedup:.2f}x")
    print(f"   UNet: {unet_speedup:.2f}x")
    
    print(f"\n🔍 开销分析:")
    print(f"   内存传输开销: {transfer_overhead:.2f}ms")
    print(f"   同步开销: {sync_overhead:.2f}ms")
    
    # 6. 问题诊断
    print(f"\n🎯 性能问题诊断:")
    
    if obs_speedup < 1.5:
        print(f"   ⚠️ obs_encoder加速比偏低 ({obs_speedup:.2f}x)")
        print(f"      可能原因: FP16优化不充分、内存带宽限制")
    
    if unet_speedup < 2.0:
        print(f"   ⚠️ UNet加速比偏低 ({unet_speedup:.2f}x)")
        print(f"      可能原因: 扩散模型优化不充分、批次大小过小")
    
    if transfer_overhead > 50:
        print(f"   ⚠️ 内存传输开销过高 ({transfer_overhead:.2f}ms)")
        print(f"      建议: 使用GPU内存池、减少数据传输")
    
    if sync_overhead > 20:
        print(f"   ⚠️ 同步开销过高 ({sync_overhead:.2f}ms)")
        print(f"      建议: 使用异步执行、CUDA图优化")
    
    # 估算理论最佳性能
    theoretical_best = trt_results['obs_encoder'] + trt_results['unet_single'] * 20  # 假设20步扩散
    print(f"\n📈 理论最佳性能估算:")
    print(f"   理论最快时间: {theoretical_best:.2f}ms")
    print(f"   当前实际时间: {trt_results['full_pipeline']:.2f}ms")
    print(f"   优化潜力: {(trt_results['full_pipeline'] - theoretical_best):.2f}ms")

if __name__ == "__main__":
    main()
