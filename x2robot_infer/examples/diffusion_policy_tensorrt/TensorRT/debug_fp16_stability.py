#!/usr/bin/env python3
"""
调试FP16数值稳定性问题
分析不同输入条件下UNet的行为
"""

import torch
import numpy as np
from tensorrt_diffusion_pipeline import TensorRTEngine

def test_unet_stability():
    """测试UNet在不同输入条件下的数值稳定性"""
    
    print("🔍 UNet FP16数值稳定性调试")
    print("=" * 60)
    
    # 加载TensorRT引擎
    engine_path = "correct_trt_engines_working_method/unet.trt"
    try:
        engine = TensorRTEngine(engine_path)
        print(f"✅ TensorRT引擎加载成功: {engine_path}")
    except Exception as e:
        print(f"❌ TensorRT引擎加载失败: {e}")
        return
    
    # 测试用例：从简单到复杂
    test_cases = [
        {
            'name': '全零输入_t0',
            'sample': torch.zeros(1, 20, 14),
            'timestep': torch.tensor([0], dtype=torch.int64),
            'global_cond': torch.zeros(1, 6158)
        },
        {
            'name': '全零输入_t1',
            'sample': torch.zeros(1, 20, 14),
            'timestep': torch.tensor([1], dtype=torch.int64),
            'global_cond': torch.zeros(1, 6158)
        },
        {
            'name': '全零输入_t5',
            'sample': torch.zeros(1, 20, 14),
            'timestep': torch.tensor([5], dtype=torch.int64),
            'global_cond': torch.zeros(1, 6158)
        },
        {
            'name': '小值输入_t0',
            'sample': torch.ones(1, 20, 14) * 0.001,
            'timestep': torch.tensor([0], dtype=torch.int64),
            'global_cond': torch.ones(1, 6158) * 0.001
        },
        {
            'name': '小值输入_t5',
            'sample': torch.ones(1, 20, 14) * 0.001,
            'timestep': torch.tensor([5], dtype=torch.int64),
            'global_cond': torch.ones(1, 6158) * 0.001
        },
        {
            'name': '中等值输入_t0',
            'sample': torch.ones(1, 20, 14) * 0.1,
            'timestep': torch.tensor([0], dtype=torch.int64),
            'global_cond': torch.ones(1, 6158) * 0.1
        },
        {
            'name': '中等值输入_t5',
            'sample': torch.ones(1, 20, 14) * 0.1,
            'timestep': torch.tensor([5], dtype=torch.int64),
            'global_cond': torch.ones(1, 6158) * 0.1
        },
        {
            'name': '随机输入_t0',
            'sample': torch.randn(1, 20, 14) * 0.1,
            'timestep': torch.tensor([0], dtype=torch.int64),
            'global_cond': torch.randn(1, 6158) * 0.1
        },
        {
            'name': '随机输入_t5',
            'sample': torch.randn(1, 20, 14) * 0.1,
            'timestep': torch.tensor([5], dtype=torch.int64),
            'global_cond': torch.randn(1, 6158) * 0.1
        }
    ]
    
    print(f"\n📊 测试 {len(test_cases)} 个用例:")
    print("-" * 60)
    
    results = []
    
    for i, test_case in enumerate(test_cases):
        print(f"\n{i+1}. 测试: {test_case['name']}")
        
        try:
            # 准备输入
            inputs = {
                'sample': test_case['sample'].cpu().numpy().astype(np.float32),
                'timestep': test_case['timestep'].cpu().numpy().astype(np.int64),
                'global_cond': test_case['global_cond'].cpu().numpy().astype(np.float32)
            }
            
            # 运行推理
            outputs = engine.infer(inputs)
            noise_pred = outputs['noise_pred']
            
            # 检查输出
            if isinstance(noise_pred, np.ndarray):
                mean_val = np.mean(noise_pred)
                std_val = np.std(noise_pred)
                has_nan = np.isnan(noise_pred).any()
                has_inf = np.isinf(noise_pred).any()
            else:
                mean_val = float(noise_pred.mean())
                std_val = float(noise_pred.std())
                has_nan = torch.isnan(noise_pred).any()
                has_inf = torch.isinf(noise_pred).any()
            
            status = "✅ 正常" if not (has_nan or has_inf) else "❌ 异常"
            
            print(f"   输出: mean={mean_val:.6f}, std={std_val:.6f}")
            print(f"   状态: {status}")
            if has_nan:
                print(f"   ⚠️  包含NaN值")
            if has_inf:
                print(f"   ⚠️  包含Inf值")
                
            results.append({
                'name': test_case['name'],
                'mean': mean_val,
                'std': std_val,
                'has_nan': has_nan,
                'has_inf': has_inf,
                'status': 'normal' if not (has_nan or has_inf) else 'abnormal'
            })
            
        except Exception as e:
            print(f"   ❌ 推理失败: {e}")
            results.append({
                'name': test_case['name'],
                'status': 'error',
                'error': str(e)
            })
    
    # 分析结果
    print("\n" + "=" * 60)
    print("📈 结果分析:")
    print("-" * 60)
    
    normal_cases = [r for r in results if r.get('status') == 'normal']
    abnormal_cases = [r for r in results if r.get('status') == 'abnormal']
    error_cases = [r for r in results if r.get('status') == 'error']
    
    print(f"✅ 正常用例: {len(normal_cases)}/{len(results)}")
    print(f"❌ 异常用例: {len(abnormal_cases)}/{len(results)}")
    print(f"💥 错误用例: {len(error_cases)}/{len(results)}")
    
    if normal_cases:
        print(f"\n✅ 正常用例:")
        for case in normal_cases:
            print(f"   - {case['name']}")
    
    if abnormal_cases:
        print(f"\n❌ 异常用例:")
        for case in abnormal_cases:
            print(f"   - {case['name']}")
    
    if error_cases:
        print(f"\n💥 错误用例:")
        for case in error_cases:
            print(f"   - {case['name']}: {case.get('error', 'Unknown error')}")
    
    return results

if __name__ == "__main__":
    # 设置随机种子确保可重现性
    torch.manual_seed(42)
    np.random.seed(42)
    
    results = test_unet_stability()
