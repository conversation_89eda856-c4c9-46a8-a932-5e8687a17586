#!/usr/bin/env python3
"""
Testing of TensorRT Pipeline
"""

import os
import sys
import torch
import numpy as np
import time
from pathlib import Path
import traceback
from typing import Dict, Any
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import numpy as np

# 添加项目根目录到路径
ROOT_DIR = str(Path(__file__).parent.parent)
sys.path.append(ROOT_DIR)

from correct_tensorrt_pipeline import CorrectTensorRTDiffusionPipeline

# 删除复杂的可视化函数，只保留简化版本

def create_simple_trajectory_plot(ground_truth, pytorch_pred, tensorrt_pred, save_path="simple_trajectory.png"):
    """
    创建简化的轨迹对比图，只显示Left Arm X Position和Right Arm X Position
    """
    try:
        print(f"🎨 生成简化轨迹对比图...")

        # 转换为numpy
        gt_np = ground_truth.cpu().numpy()[0]  # [horizon, action_dim]
        pytorch_np = pytorch_pred.cpu().numpy()[0]
        tensorrt_np = tensorrt_pred.cpu().numpy()[0]

        horizon, action_dim = gt_np.shape
        time_steps = np.arange(horizon)

        # 创建1x2子图，只显示左臂和右臂的X位置
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))

        # 1. 左臂X位置轨迹
        ax1 = axes[0]
        if action_dim >= 1:
            ax1.plot(time_steps, gt_np[:, 0], 'g-', linewidth=3, label='Ground Truth', alpha=0.8)
            ax1.plot(time_steps, pytorch_np[:, 0], 'b--', linewidth=2, label='PyTorch', alpha=0.7)
            ax1.plot(time_steps, tensorrt_np[:, 0], 'r:', linewidth=2, label='TensorRT', alpha=0.7)
        ax1.set_title('Left Arm X Position', fontsize=14)
        ax1.set_xlabel('Time Step', fontsize=12)
        ax1.set_ylabel('Position', fontsize=12)
        ax1.legend(fontsize=11)
        ax1.grid(True, alpha=0.3)

        # 2. 右臂X位置轨迹
        ax2 = axes[1]
        if action_dim >= 8:  # 右臂X位置通常在第7个索引（0-based）
            ax2.plot(time_steps, gt_np[:, 7], 'g-', linewidth=3, label='Ground Truth', alpha=0.8)
            ax2.plot(time_steps, pytorch_np[:, 7], 'b--', linewidth=2, label='PyTorch', alpha=0.7)
            ax2.plot(time_steps, tensorrt_np[:, 7], 'r:', linewidth=2, label='TensorRT', alpha=0.7)
        ax2.set_title('Right Arm X Position', fontsize=14)
        ax2.set_xlabel('Time Step', fontsize=12)
        ax2.set_ylabel('Position', fontsize=12)
        ax2.legend(fontsize=11)
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(save_path, dpi=200, bbox_inches='tight')
        print(f"   ✅ 简化轨迹图已保存到: {save_path}")

        # 打印统计信息
        print(f"\n📈 轨迹统计信息:")
        print(f"   时间步数: {horizon}")
        print(f"   动作维度: {action_dim}")

        if action_dim >= 1:
            left_gt_mean = np.mean(gt_np[:, 0])
            left_pytorch_mean = np.mean(pytorch_np[:, 0])
            left_tensorrt_mean = np.mean(tensorrt_np[:, 0])
            print(f"   左臂X位置均值: GT={left_gt_mean:.4f}, PyTorch={left_pytorch_mean:.4f}, TensorRT={left_tensorrt_mean:.4f}")

        if action_dim >= 8:
            right_gt_mean = np.mean(gt_np[:, 7])
            right_pytorch_mean = np.mean(pytorch_np[:, 7])
            right_tensorrt_mean = np.mean(tensorrt_np[:, 7])
            print(f"   右臂X位置均值: GT={right_gt_mean:.4f}, PyTorch={right_pytorch_mean:.4f}, TensorRT={right_tensorrt_mean:.4f}")

        plt.close()
        return True

    except Exception as e:
        print(f"❌ 简化轨迹图生成失败: {e}")
        traceback.print_exc()
        return False

def create_multi_sample_trajectory_plot(all_ground_truths, all_pytorch_preds, all_tensorrt_preds, save_path="multi_sample_trajectory.png"):
    """
    创建多样本轨迹对比图，显示多个样本的Left Arm X Position和Right Arm X Position
    """
    try:
        print(f"🎨 生成多样本轨迹对比图...")

        num_samples = len(all_ground_truths)
        if num_samples == 0:
            print(f"   ❌ 没有样本数据")
            return False

        # 创建子图：2行，每行显示多个样本
        fig, axes = plt.subplots(2, min(num_samples, 3), figsize=(15, 8))  # 最多显示3个样本
        if num_samples == 1:
            axes = axes.reshape(2, 1)
        elif min(num_samples, 3) == 1:
            axes = axes.reshape(2, 1)

        samples_to_show = min(num_samples, 3)

        for sample_idx in range(samples_to_show):
            gt_np = all_ground_truths[sample_idx].numpy()[0]  # [horizon, action_dim]
            pytorch_np = all_pytorch_preds[sample_idx].numpy()[0]
            tensorrt_np = all_tensorrt_preds[sample_idx].numpy()[0]

            horizon, action_dim = gt_np.shape
            time_steps = np.arange(horizon)

            # 左臂X位置 (第一行)
            ax_left = axes[0, sample_idx] if samples_to_show > 1 else axes[0]
            if action_dim >= 1:
                ax_left.plot(time_steps, gt_np[:, 0], 'g-', linewidth=3, label='Ground Truth', alpha=0.8)
                ax_left.plot(time_steps, pytorch_np[:, 0], 'b--', linewidth=2, label='PyTorch', alpha=0.7)
                ax_left.plot(time_steps, tensorrt_np[:, 0], 'r:', linewidth=2, label='TensorRT', alpha=0.7)
            ax_left.set_title(f'Sample {sample_idx+1}: Left Arm X Position', fontsize=12)
            ax_left.set_xlabel('Time Step', fontsize=10)
            ax_left.set_ylabel('Position', fontsize=10)
            if sample_idx == 0:  # 只在第一个子图显示图例
                ax_left.legend(fontsize=9)
            ax_left.grid(True, alpha=0.3)

            # 右臂X位置 (第二行)
            ax_right = axes[1, sample_idx] if samples_to_show > 1 else axes[1]
            if action_dim >= 8:
                ax_right.plot(time_steps, gt_np[:, 7], 'g-', linewidth=3, label='Ground Truth', alpha=0.8)
                ax_right.plot(time_steps, pytorch_np[:, 7], 'b--', linewidth=2, label='PyTorch', alpha=0.7)
                ax_right.plot(time_steps, tensorrt_np[:, 7], 'r:', linewidth=2, label='TensorRT', alpha=0.7)
            ax_right.set_title(f'Sample {sample_idx+1}: Right Arm X Position', fontsize=12)
            ax_right.set_xlabel('Time Step', fontsize=10)
            ax_right.set_ylabel('Position', fontsize=10)
            if sample_idx == 0:  # 只在第一个子图显示图例
                ax_right.legend(fontsize=9)
            ax_right.grid(True, alpha=0.3)

            # 打印样本统计信息
            left_gt_mean = np.mean(gt_np[:, 0]) if action_dim >= 1 else 0
            left_pytorch_mean = np.mean(pytorch_np[:, 0]) if action_dim >= 1 else 0
            left_tensorrt_mean = np.mean(tensorrt_np[:, 0]) if action_dim >= 1 else 0

            right_gt_mean = np.mean(gt_np[:, 7]) if action_dim >= 8 else 0
            right_pytorch_mean = np.mean(pytorch_np[:, 7]) if action_dim >= 8 else 0
            right_tensorrt_mean = np.mean(tensorrt_np[:, 7]) if action_dim >= 8 else 0

            print(f"   样本 {sample_idx+1}:")
            print(f"     左臂X: GT={left_gt_mean:.4f}, PyTorch={left_pytorch_mean:.4f}, TRT={left_tensorrt_mean:.4f}")
            print(f"     右臂X: GT={right_gt_mean:.4f}, PyTorch={right_pytorch_mean:.4f}, TRT={right_tensorrt_mean:.4f}")

        plt.tight_layout()
        plt.savefig(save_path, dpi=200, bbox_inches='tight')
        print(f"   ✅ 多样本轨迹图已保存到: {save_path}")
        plt.close()

        return True

    except Exception as e:
        print(f"❌ 多样本轨迹图生成失败: {e}")
        traceback.print_exc()
        return False

def get_real_data_batch(sample_index: int = 0):
    """获取真实数据批次

    Args:
        sample_index: 样本索引，用于获取不同的样本
    """
    try:
        print(f"🔄 正在获取真实数据 (样本索引: {sample_index})...")

        # 导入数据集加载器
        from simple_dataset_loader import SimpleDatasetLoader

        # 创建数据集加载器
        loader = SimpleDatasetLoader()

        # 获取指定索引的批次数据
        batch = loader.get_single_batch_with_retry(max_retries=2, timeout_per_try=45, skip_batches=sample_index)

        # 打印详细的样本信息
        print(f"📊 详细样本信息:")
        loader.print_sample_info(batch)

        # 准备推理数据
        obs_dict = loader.prepare_obs_for_inference(batch)
        ground_truth = loader.get_ground_truth_action(batch)

        print(f"✅ 成功获取真实数据")
        print(f"   观测键: {list(obs_dict['obs'].keys())}")
        print(f"   真实动作形状: {ground_truth.shape}")

        return obs_dict, ground_truth

    except Exception as e:
        print(f"❌ 获取真实数据失败: {e}")
        traceback.print_exc()
        return None, None

def get_multiple_real_data_samples(num_samples: int = 5):
    """获取多个真实数据样本"""
    try:
        print(f"🔄 正在获取 {num_samples} 个真实数据样本...")

        # 导入数据集加载器
        from simple_dataset_loader import SimpleDatasetLoader

        # 创建数据集加载器
        loader = SimpleDatasetLoader()

        # 初始化数据集
        loader._initialize_dataset()

        # 等待数据处理完成
        if not loader.wait_for_data_ready(timeout_seconds=600):
            print(f"❌ 数据处理超时")
            return []

        samples = []
        for i in range(num_samples):
            try:
                print(f"   获取样本 {i+1}/{num_samples}...")

                # 获取单个批次数据
                batch = loader.get_sample_batch(timeout_seconds=45)
                if batch is None:
                    print(f"   ⚠️ 样本 {i+1} 获取失败，跳过")
                    continue

                # 准备推理数据
                obs_dict = loader.prepare_obs_for_inference(batch)
                ground_truth = loader.get_ground_truth_action(batch)

                samples.append({
                    'obs_dict': obs_dict,
                    'ground_truth': ground_truth,
                    'sample_idx': i + 1
                })

                print(f"   ✅ 样本 {i+1} 获取成功")

            except Exception as e:
                print(f"   ❌ 样本 {i+1} 获取失败: {e}")
                continue

        # 清理资源
        loader.cleanup()

        print(f"✅ 成功获取 {len(samples)}/{num_samples} 个样本")
        return samples

    except Exception as e:
        print(f"❌ 获取多个真实数据样本失败: {e}")
        traceback.print_exc()
        return []

def create_test_observations(batch_size: int = 1, device: str = 'cuda', test_type: str = 'fixed') -> Dict[str, torch.Tensor]:
    """
    创建测试观测数据
    
    Args:
        batch_size: 批次大小
        device: 设备
        test_type: 测试类型 ('fixed', 'random', 'zeros')
    
    Returns:
        obs_dict: 观测字典
    """
    if test_type == 'fixed':
        # 固定值测试（图像=0.5，agent_pos=0）
        obs_data = {
            'face_view': torch.ones(batch_size, 1, 3, 480, 640, device=device) * 0.5,
            'left_wrist_view': torch.ones(batch_size, 1, 3, 480, 640, device=device) * 0.5,
            'right_wrist_view': torch.ones(batch_size, 1, 3, 480, 640, device=device) * 0.5,
            'agent_pos': torch.zeros(batch_size, 1, 14, device=device)
        }
    elif test_type == 'zeros':
        # 全零测试
        obs_data = {
            'face_view': torch.zeros(batch_size, 1, 3, 480, 640, device=device),
            'left_wrist_view': torch.zeros(batch_size, 1, 3, 480, 640, device=device),
            'right_wrist_view': torch.zeros(batch_size, 1, 3, 480, 640, device=device),
            'agent_pos': torch.zeros(batch_size, 1, 14, device=device)
        }
    elif test_type == 'random':
        # 随机测试（模拟真实图像范围）
        obs_data = {
            'face_view': torch.randn(batch_size, 1, 3, 480, 640, device=device) * 50 + 128,
            'left_wrist_view': torch.randn(batch_size, 1, 3, 480, 640, device=device) * 50 + 128,
            'right_wrist_view': torch.randn(batch_size, 1, 3, 480, 640, device=device) * 50 + 128,
            'agent_pos': torch.randn(batch_size, 1, 14, device=device)
        }
        # 确保图像值在合理范围内
        for key in ['face_view', 'left_wrist_view', 'right_wrist_view']:
            obs_data[key] = torch.clamp(obs_data[key], 0, 255)
    else:
        raise ValueError(f"未知的测试类型: {test_type}")
    
    return {'obs': obs_data}

def test_accuracy_comparison(pipeline: CorrectTensorRTDiffusionPipeline,
                           test_cases: list = None,
                           verbose: bool = True) -> Dict[str, Dict[str, float]]:
    """
    测试TensorRT和PyTorch的精度对比

    Args:
        pipeline: TensorRT pipeline
        test_cases: 测试案例列表
        verbose: 是否输出详细信息

    Returns:
        results: 精度对比结果
    """
    if test_cases is None:
        test_cases = ['fixed', 'zeros', 'deterministic_random']

    if pipeline.pytorch_policy is None:
        print("⚠️ PyTorch模型未加载，跳过精度对比")
        return {}

    results = {}

    print(f"\n=== TensorRT vs PyTorch 精度对比测试 ===")
    print(f"   使用确定性推理确保公平对比")

    for test_type in test_cases:
        if verbose:
            print(f"\n--- 测试案例: {test_type} ---")

        try:
            # 设置确定性种子
            torch.manual_seed(42)
            np.random.seed(42)

            # 创建测试数据
            if test_type == 'deterministic_random':
                # 使用确定性随机数据
                obs_dict = create_test_observations(batch_size=1, test_type='random')
            else:
                obs_dict = create_test_observations(batch_size=1, test_type=test_type)

            if verbose:
                print(f"   创建测试数据: {test_type}")
                for key, value in obs_dict['obs'].items():
                    print(f"     {key}: shape={value.shape}, mean={torch.mean(value).item():.6f}, std={torch.std(value).item():.6f}")

            # 分别计算TensorRT和PyTorch的推理时间
            if verbose:
                print(f"   运行确定性对比...")

            # 预热：TensorRT和PyTorch各预热5次（减少预热开销）
            if verbose:
                print(f"   预热TensorRT和PyTorch...")
            for _ in range(5):
                _ = pipeline.predict_action(obs_dict, deterministic=True)
                _ = pipeline.get_pytorch_prediction(obs_dict, deterministic=True)
            torch.cuda.synchronize()

            # TensorRT推理时间测试（单次精确测量）
            torch.cuda.synchronize()
            trt_start_time = time.perf_counter()
            trt_result = pipeline.predict_action(obs_dict, deterministic=True)
            torch.cuda.synchronize()
            trt_time = (time.perf_counter() - trt_start_time) * 1000

            # PyTorch推理时间测试（单次精确测量）
            torch.cuda.synchronize()
            pytorch_start_time = time.perf_counter()
            pytorch_result = pipeline.get_pytorch_prediction(obs_dict, deterministic=True)
            torch.cuda.synchronize()
            pytorch_time = (time.perf_counter() - pytorch_start_time) * 1000

            # 计算对比指标
            trt_action = trt_result['action_pred']
            pytorch_action = pytorch_result['action_pred']

            # 计算误差指标
            diff = torch.abs(trt_action - pytorch_action)
            mse = torch.mean((trt_action - pytorch_action) ** 2).item()
            mae = torch.mean(diff).item()
            max_diff = torch.max(diff).item()

            # 计算相对误差
            pytorch_magnitude = torch.mean(torch.abs(pytorch_action)).item()
            relative_error = mae / pytorch_magnitude if pytorch_magnitude > 0 else float('inf')

            # 计算相关系数
            trt_flat = trt_action.flatten()
            pytorch_flat = pytorch_action.flatten()
            correlation = torch.corrcoef(torch.stack([trt_flat, pytorch_flat]))[0, 1].item()

            # 构建对比指标
            comparison_metrics = {
                'mse': mse,
                'mae': mae,
                'max_diff': max_diff,
                'relative_error': relative_error,
                'correlation': correlation,
                'trt_mean': torch.mean(trt_action).item(),
                'trt_std': torch.std(trt_action).item(),
                'pytorch_mean': torch.mean(pytorch_action).item(),
                'pytorch_std': torch.std(pytorch_action).item(),
                'trt_time_ms': trt_time,
                'pytorch_time_ms': pytorch_time,
                'speedup': pytorch_time / trt_time if trt_time > 0 else 1.0
            }

            total_time = trt_time + pytorch_time

            if verbose:
                print(f"   对比结果:")
                print(f"     TensorRT输出: mean={comparison_metrics['trt_mean']:.6f}, std={comparison_metrics['trt_std']:.6f}")
                print(f"     PyTorch输出: mean={comparison_metrics['pytorch_mean']:.6f}, std={comparison_metrics['pytorch_std']:.6f}")
                print(f"     MSE: {comparison_metrics['mse']:.8f}")
                print(f"     MAE: {comparison_metrics['mae']:.8f}")
                print(f"     Max差异: {comparison_metrics['max_diff']:.8f}")
                print(f"     相对误差: {comparison_metrics['relative_error']:.8f}")
                print(f"     相关系数: {comparison_metrics['correlation']:.8f}")
                print(f"     TensorRT推理时间: {comparison_metrics['trt_time_ms']:.2f}ms")
                print(f"     PyTorch推理时间: {comparison_metrics['pytorch_time_ms']:.2f}ms")
                print(f"     加速比: {comparison_metrics['speedup']:.2f}x")
                print(f"     总时间: {total_time:.2f}ms")

            # 判断精度等级
            mse = comparison_metrics['mse']
            max_diff = comparison_metrics['max_diff']

            if mse < 1e-4:
                accuracy_level = "高精度一致"
                status = "✅"
            elif mse < 1e-3:
                accuracy_level = "可接受精度"
                status = "✅"
            elif mse < 1e-2:
                accuracy_level = "低精度但可用"
                status = "⚠️"
            else:
                accuracy_level = "精度不足"
                status = "❌"

            if verbose:
                print(f"     精度等级: {status} {accuracy_level}")

            # 保存结果
            results[test_type] = {
                **comparison_metrics,
                'total_time_ms': total_time,
                'accuracy_level': accuracy_level,
                'status': status
            }

        except Exception as e:
            print(f"   ❌ 测试案例 {test_type} 失败: {e}")
            traceback.print_exc()
            results[test_type] = {'error': str(e)}

    return results

def test_obs_encoder_accuracy(pipeline: CorrectTensorRTDiffusionPipeline) -> Dict[str, float]:
    """
    单独测试obs_encoder的精度
    """
    print(f"\n=== obs_encoder 精度测试 ===")
    
    if pipeline.pytorch_policy is None:
        print("⚠️ PyTorch模型未加载，跳过obs_encoder精度测试")
        return {}
    
    try:
        # 创建测试数据
        obs_dict = create_test_observations(batch_size=1, test_type='fixed')
        
        # TensorRT obs_encoder
        trt_encoded = pipeline._encode_observations(obs_dict)
        
        # PyTorch obs_encoder（使用原始PyTorch模型）
        if pipeline.pytorch_policy is None:
            print("   ⚠️ PyTorch模型未加载，跳过obs_encoder对比")
            return {}

        with torch.no_grad():
            # 使用原始PyTorch模型的完整流程
            obs_data = obs_dict['obs']

            # 应用normalizer（忽略RGB keys）
            nobs = pipeline.pytorch_policy.normalizer.normalize(obs_data, ignore_keys=pipeline.pytorch_policy.rgb_keys)
            # 移除instruction键（如果存在）
            this_nobs = {k: v for k, v in nobs.items() if k != 'instruction'}
            # 通过obs_encoder
            pytorch_encoded = pipeline.pytorch_policy.obs_encoder(this_nobs)
            # 展平输出
            pytorch_encoded = pytorch_encoded.reshape(pytorch_encoded.shape[0], -1)
        
        # 计算误差
        diff = torch.abs(trt_encoded - pytorch_encoded)
        mse = torch.mean((trt_encoded - pytorch_encoded) ** 2).item()
        mae = torch.mean(diff).item()
        max_diff = torch.max(diff).item()
        
        print(f"   TensorRT编码: mean={torch.mean(trt_encoded).item():.6f}, std={torch.std(trt_encoded).item():.6f}")
        print(f"   PyTorch编码: mean={torch.mean(pytorch_encoded).item():.6f}, std={torch.std(pytorch_encoded).item():.6f}")
        print(f"   MSE: {mse:.8f}")
        print(f"   MAE: {mae:.8f}")
        print(f"   Max差异: {max_diff:.8f}")
        
        if mse < 1e-4:
            print(f"   ✅ obs_encoder精度良好")
        else:
            print(f"   ⚠️ obs_encoder精度需要改进")
        
        return {
            'mse': mse,
            'mae': mae,
            'max_diff': max_diff
        }
        
    except Exception as e:
        print(f"   ❌ obs_encoder精度测试失败: {e}")
        traceback.print_exc()
        return {'error': str(e)}

def print_summary_results(results: Dict[str, Dict[str, float]]):
    """打印汇总结果"""
    print(f"\n{'='*60}")
    print(f"精度测试结果汇总")
    print(f"{'='*60}")
    
    if not results:
        print("没有可用的测试结果")
        return
    
    # 创建表格
    headers = ["测试案例", "MSE", "MAE", "Max差异", "相对误差", "TRT时间(ms)", "PyTorch时间(ms)", "加速比", "状态"]

    print(f"{headers[0]:<12} {headers[1]:<12} {headers[2]:<12} {headers[3]:<12} {headers[4]:<12} {headers[5]:<12} {headers[6]:<15} {headers[7]:<8} {headers[8]}")
    print("-" * 120)
    
    for test_type, metrics in results.items():
        if 'error' in metrics:
            print(f"{test_type:<12} {'ERROR':<60}")
            continue
        
        mse = metrics.get('mse', 0)
        mae = metrics.get('mae', 0)
        max_diff = metrics.get('max_diff', 0)
        rel_err = metrics.get('relative_error', 0)
        trt_time = metrics.get('trt_time_ms', 0)
        pytorch_time = metrics.get('pytorch_time_ms', 0)
        speedup = metrics.get('speedup', 0)
        status = metrics.get('status', '❌')

        print(f"{test_type:<12} {mse:<12.6f} {mae:<12.6f} {max_diff:<12.6f} {rel_err:<12.6f} {trt_time:<12.2f} {pytorch_time:<15.2f} {speedup:<8.2f}x {status}")
    
    # 计算平均指标
    valid_results = [r for r in results.values() if 'error' not in r]
    if valid_results:
        avg_mse = np.mean([r['mse'] for r in valid_results])
        avg_mae = np.mean([r['mae'] for r in valid_results])
        avg_trt_time = np.mean([r.get('trt_time_ms', 0) for r in valid_results])
        avg_pytorch_time = np.mean([r.get('pytorch_time_ms', 0) for r in valid_results])
        avg_speedup = np.mean([r.get('speedup', 1.0) for r in valid_results])

        print("-" * 120)
        print(f"{'平均':<12} {avg_mse:<12.6f} {avg_mae:<12.6f} {'N/A':<12} {'N/A':<12} {avg_trt_time:<12.2f} {avg_pytorch_time:<15.2f} {avg_speedup:<8.2f}x")
        
        # 总体评估
        print(f"\n{'='*60}")
        print(f"总体评估")
        print(f"{'='*60}")

        # 精度评估
        if avg_mse < 1e-4:
            print(f"🎉 精度评估: 优秀 (MSE < 1e-4)")
        elif avg_mse < 1e-2:
            print(f"✅ 精度评估: 良好 (MSE < 1e-2)")
        else:
            print(f"⚠️ 精度评估: 需要改进 (MSE >= 1e-2)")

        # 性能评估
        if avg_speedup > 2.0:
            print(f"🚀 性能评估: 优秀 (加速比 {avg_speedup:.2f}x)")
        elif avg_speedup > 1.2:
            print(f"✅ 性能评估: 良好 (加速比 {avg_speedup:.2f}x)")
        elif avg_speedup > 0.8:
            print(f"⚠️ 性能评估: 基本持平 (加速比 {avg_speedup:.2f}x)")
        else:
            print(f"❌ 性能评估: 性能下降 (加速比 {avg_speedup:.2f}x)")

        print(f"\n推理时间详情:")
        print(f"  TensorRT平均推理时间: {avg_trt_time:.2f}ms")
        print(f"  PyTorch平均推理时间: {avg_pytorch_time:.2f}ms")
        print(f"  时间节省: {avg_pytorch_time - avg_trt_time:.2f}ms ({((avg_pytorch_time - avg_trt_time) / avg_pytorch_time * 100):.1f}%)")

def main(use_real_data: bool = False):
    """主函数"""
    checkpoint_path = "/workspace/x2robot_infer/examples/diffusion_policy_tensorrt/TensorRT/epoch=0030-train_loss=0.005.ckpt"
    engine_dir = "correct_trt_engines_working_method"

    # 检查必要文件
    if not os.path.exists(checkpoint_path):
        print(f"❌ Checkpoint文件不存在: {checkpoint_path}")
        return False

    if not os.path.exists(engine_dir):
        print(f"❌ 引擎目录不存在: {engine_dir}")
        return False

    try:
        data_source = "真实数据" if use_real_data else "虚拟数据"
        print(f"🚀 开始测试正确的TensorRT Pipeline (使用{data_source})")
        print(f"   引擎目录: {engine_dir}")
        print(f"   Checkpoint: {os.path.basename(checkpoint_path)}")

        # 创建pipeline
        pipeline = CorrectTensorRTDiffusionPipeline(
            engine_dir=engine_dir,
            checkpoint_path=checkpoint_path,
            num_inference_steps=100,  # 设置为100步
            device='cuda',
            verbose=False
        )
        
        # 1. 测试obs_encoder精度
        obs_encoder_results = test_obs_encoder_accuracy(pipeline)
        
        # 2. 测试完整pipeline精度
        if use_real_data:
            # 使用真实数据测试
            print(f"\n🔍 使用真实数据进行测试...")
            obs_dict, ground_truth = get_real_data_batch(sample_index=0)

            if obs_dict is not None and ground_truth is not None:
                test_cases = ['real_data']
                accuracy_results = {}

                try:
                    print(f"   观测键: {list(obs_dict['obs'].keys())}")
                    print(f"   真实动作形状: {ground_truth.shape}")

                    # 进行推理
                    print(f"   正在进行TensorRT推理...")
                    trt_result_dict = pipeline.predict_action(obs_dict, deterministic=True)
                    trt_result = trt_result_dict['action']  # 提取动作张量

                    print(f"   正在进行PyTorch推理...")
                    pytorch_result_dict = pipeline.get_pytorch_prediction(obs_dict, deterministic=True)
                    pytorch_result = pytorch_result_dict['action']  # 提取动作张量

                    # 重要：数据尺度对齐
                    # - Ground Truth (batch['action']) 是归一化后的数据，范围通常在[-1, 1]
                    # - 模型预测通过predict_action()已经反归一化到原始尺度
                    # 为了正确对比，需要将Ground Truth也反归一化到原始尺度

                    # 对Ground Truth进行反归一化，使其与模型预测在同一尺度
                    if hasattr(pipeline.pytorch_policy, 'normalizer') and pipeline.pytorch_policy.normalizer is not None:
                        try:
                            ground_truth_unnormalized = pipeline.pytorch_policy.normalizer['action'].unnormalize(ground_truth)
                            print(f"   ✅ Ground Truth已反归一化到原始尺度")
                            ground_truth = ground_truth_unnormalized
                        except Exception as e:
                            print(f"   ⚠️ Ground Truth反归一化失败，使用原始数据: {e}")
                    else:
                        print(f"   ⚠️ 无法获取normalizer，Ground Truth可能不在正确尺度")
                    print(f"   Ground truth数据统计:")
                    print(f"     形状: {ground_truth.shape}")
                    print(f"     均值: {ground_truth.mean().item():.6f}, 标准差: {ground_truth.std().item():.6f}")
                    print(f"     最小值: {ground_truth.min().item():.6f}, 最大值: {ground_truth.max().item():.6f}")

                    print(f"   TensorRT预测数据统计:")
                    print(f"     形状: {trt_result.shape}")
                    print(f"     均值: {trt_result.mean().item():.6f}, 标准差: {trt_result.std().item():.6f}")
                    print(f"     最小值: {trt_result.min().item():.6f}, 最大值: {trt_result.max().item():.6f}")

                    print(f"   PyTorch预测数据统计:")
                    print(f"     形状: {pytorch_result.shape}")
                    print(f"     均值: {pytorch_result.mean().item():.6f}, 标准差: {pytorch_result.std().item():.6f}")
                    print(f"     最小值: {pytorch_result.min().item():.6f}, 最大值: {pytorch_result.max().item():.6f}")

                    # 计算精度 - TensorRT vs PyTorch
                    trt_pytorch_mse = torch.mean((trt_result - pytorch_result) ** 2).item()
                    trt_pytorch_mae = torch.mean(torch.abs(trt_result - pytorch_result)).item()
                    trt_pytorch_max_diff = torch.max(torch.abs(trt_result - pytorch_result)).item()

                    # 计算与Ground Truth的误差
                    pytorch_gt_mse = torch.mean((pytorch_result - ground_truth) ** 2).item()
                    pytorch_gt_mae = torch.mean(torch.abs(pytorch_result - ground_truth)).item()
                    pytorch_gt_max_diff = torch.max(torch.abs(pytorch_result - ground_truth)).item()

                    tensorrt_gt_mse = torch.mean((trt_result - ground_truth) ** 2).item()
                    tensorrt_gt_mae = torch.mean(torch.abs(trt_result - ground_truth)).item()
                    tensorrt_gt_max_diff = torch.max(torch.abs(trt_result - ground_truth)).item()

                    accuracy_results['real_data'] = {
                        'mse': trt_pytorch_mse,
                        'mae': trt_pytorch_mae,
                        'max_diff': trt_pytorch_max_diff,
                        'status': '✅' if trt_pytorch_mse < 1e-3 else '❌',
                        'trt_result': trt_result,
                        'pytorch_result': pytorch_result,
                        'ground_truth': ground_truth
                    }

                    print(f"   ✅ 真实数据测试完成")
                    print(f"     TensorRT vs PyTorch: MSE={trt_pytorch_mse:.8f}, MAE={trt_pytorch_mae:.6f}, Max={trt_pytorch_max_diff:.6f}")
                    print(f"     PyTorch vs GT: MSE={pytorch_gt_mse:.8f}, MAE={pytorch_gt_mae:.6f}, Max={pytorch_gt_max_diff:.6f}")
                    print(f"     TensorRT vs GT: MSE={tensorrt_gt_mse:.8f}, MAE={tensorrt_gt_mae:.6f}, Max={tensorrt_gt_max_diff:.6f}")
                    print(f"     预测动作形状: {trt_result.shape}")
                    print(f"     真实动作形状: {ground_truth.shape}")

                    # 生成轨迹对比可视化（只显示左臂和右臂X位置）
                    print(f"\n🎨 生成轨迹对比可视化...")
                    visualization_success = create_simple_trajectory_plot(
                        ground_truth=ground_truth,
                        pytorch_pred=pytorch_result,
                        tensorrt_pred=trt_result,
                        save_path="trajectory_comparison.png"
                    )

                    if visualization_success:
                        print(f"   ✅ 轨迹可视化生成成功")
                    else:
                        print(f"   ⚠️ 轨迹可视化生成失败，但不影响测试结果")

                except Exception as e:
                    print(f"   ❌ 真实数据测试失败: {e}")
                    accuracy_results['real_data'] = {'error': str(e)}
                    traceback.print_exc()
            else:
                print(f"   ❌ 无法获取真实数据，回退到虚拟数据测试")
                test_cases = ['fixed', 'zeros', 'deterministic_random']
                accuracy_results = test_accuracy_comparison(
                    pipeline=pipeline,
                    test_cases=test_cases,
                    verbose=True
                )
        else:
            # 使用虚拟数据测试
            test_cases = ['fixed', 'zeros', 'deterministic_random']
            accuracy_results = test_accuracy_comparison(
                pipeline=pipeline,
                test_cases=test_cases,
                verbose=True
            )
        
        # 3. 打印汇总结果
        print_summary_results(accuracy_results)
        
        # 4. 判断是否成功
        if accuracy_results:
            valid_results = [r for r in accuracy_results.values() if 'error' not in r]
            if valid_results:
                avg_mse = np.mean([r['mse'] for r in valid_results])
                max_mse = max([r['mse'] for r in valid_results])

                # 更严格的成功标准
                excellent_threshold = 1e-5  # 高精度
                good_threshold = 1e-3       # 可接受精度
                acceptable_threshold = 1e-2  # 低精度但可用

                if avg_mse < excellent_threshold:
                    print(f"\n🎉 测试完全成功！平均MSE: {avg_mse:.8f} (高精度)")
                    success_level = "excellent"
                elif avg_mse < good_threshold:
                    print(f"\n✅ 测试成功！平均MSE: {avg_mse:.8f} (可接受精度)")
                    success_level = "good"
                elif avg_mse < acceptable_threshold:
                    print(f"\n⚠️ 测试基本通过，平均MSE: {avg_mse:.8f} (低精度但可用)")
                    success_level = "acceptable"
                else:
                    print(f"\n❌ 测试失败，平均MSE: {avg_mse:.8f} (精度不足)")
                    success_level = "failed"

                print(f"   最大MSE: {max_mse:.8f}")
                print(f"   成功的测试案例: {len([r for r in valid_results if r.get('status') == '✅'])}/{len(valid_results)}")

                # 返回是否达到可接受标准
                return success_level in ["excellent", "good", "acceptable"]
        
        return False
        
    except Exception as e:
        print(f"❌ 测试过程发生错误: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="测试TensorRT Pipeline")
    parser.add_argument("--real-data", action="store_true",
                       help="使用真实数据进行测试（默认使用虚拟数据）")

    args = parser.parse_args()

    success = main(use_real_data=args.real_data)
    print(f"\n{'='*60}")
    if success:
        data_type = "真实数据" if args.real_data else "虚拟数据"
        print(f"🎉 TensorRT Pipeline测试成功！(使用{data_type})")
    else:
        print(f"❌ TensorRT Pipeline测试失败")

    sys.exit(0 if success else 1)
