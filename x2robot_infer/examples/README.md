# 🚀 Triton Python Backend 配置指南

本文档介绍如何基于提供的示例配置和实现自定义的Triton Python Backend模型。在Diffusion_Policy文件夹下示例包含三个关键文件：

1. `model.py` - Python后端实现
2. `config.pbtxt` - Triton模型配置文件
3. `test_client.py` - 对应客户端测试文件

（`config.yaml` - 不需要在意的文件）

## 📂 文件结构说明

以 `examples/diffusion_policy` 为示例进行文件结构说明。

### 1. model.py - Python后端实现
这是Triton模型的核心实现，包含以下关键组件：

#### (a) 🏗️ 初始化函数 `initialize(self, args)`

该函数是Triton Server的model.py后端文件中必须存在的函数，可以理解为一个Pytorch nn.module的Init方法，主要功能是加载模型权重。

```python
def initialize(self, args):
    # 设备配置
    self.device_kind = args["model_instance_kind"]
    self.device_id = args["model_instance_device_id"]
    
    # 参数加载（支持环境变量和配置文件两种方式）
    ckpt_path = os.environ.get("CKPT_PATH") or config["parameters"]["ckpt_path"]
    self.action_interpolate_multiplier = int(...)
    
    # 模型加载
    payload = torch.load(ckpt_path, pickle_module=dill)
    self.policy = workspace.model.eval().to(device)
    
    # 历史记录初始化
    self.arm_history = ArmActionHistory(max_length=history_len)
```

**🔧 适配要点**：
1. 修改模型加载逻辑（如使用不同的模型架构）
2. 调整参数获取方式（环境变量 vs 配置文件）
3. 自定义历史记录长度等其他参数（`action_history_length`）

#### (b) ⚡ 执行函数 `execute(self, requests)`

该函数是Triton Server的model.py后端文件中必须存在的函数，可以理解为一个Pytorch nn.module的forward方法，主要功能是处理输入、执行模型。

```python
def execute(self, requests):
    for request in requests:
        # 1. 获取输入张量
        inputs = {
            "follow1_pos": pb_utils.get_input_tensor_by_name(...).as_numpy(),
            # ... 其他输入
        }
        
        # 2. 处理图像输入（JPEG字节流）
        views = {}
        for cam_name in ["CAMERA_LEFT", ...]:
            byte_data = tensor.as_numpy()[0]
            img = decompress_image(byte_data)
        
        # 3. 执行预测
        actions = self._pred_func(views, inputs)
        
        # 4. 构建输出张量
        output_tensors = [
            pb_utils.Tensor("FOLLOW1_POS", actions['follow1_pos']),
            # ... 其他输出
        ]
```

**🔧 适配要点**：
1. 修改输入处理逻辑（特别是图像解压部分）
2. 自定义输出格式和维度
3. 增强异常处理（根据实际需求）

#### (c) 🔮 预测函数 `_pred_func(self, views, actions)`

该函数为Socket版本中迁移过来的_pred_func，不是Triton Backend所刚需的，是由执行函数 `execute(self, requests)` 调用的，主要功能是具体的模型推理实现。

```python
def _pred_func(self, views, actions):
    # 1. 数据预处理
    agent_data = np.concatenate([left_data, right_data])
    
    # 2. 历史记录处理
    if self.history_len > 0:
        history_data = self.arm_history.get_history()
        agent_data = np.concatenate([history_data, agent_data])
    
    # 3. 模型推理
    with torch.no_grad():
        obs_dict = {k: torch.from_numpy(v) for k, v in views.items()}
        result = self.policy.predict_action({"obs": obs_dict})
    
    # 4. 后处理（插值+截取）
    left_action_pred = interpolates_actions(...)
    left_action_pred = left_action_pred[start_frame:end_frame]
```

**🔧 适配要点**：
1. 修改输入数据的组织方式
2. 自定义后处理逻辑（如`interpolates_actions`）
3. 调整动作截取比例（`action_start_ratio`/`action_end_ratio`）

### 2. config.pbtxt - 模型配置文件
```protobuf
name: "model"
backend: "python"

input [
  {
    name: "ACTION_FOLLOW1_POS"
    data_type: TYPE_FP32
    dims: [7]
  },
  # ... 其他输入
]

output [
  {
    name: "FOLLOW1_POS"
    data_type: TYPE_FP32
    dims: [20, 7]
  },
  # ... 其他输出
]

parameters {
  key: "ckpt_path"
  value: { string_value: "${ckpt_path}" }
}
# ... 其他参数

instance_group [
  { kind: KIND_GPU, count: 1, gpus: [0] }
]
```

**📌 关键配置说明**：
| 配置项 | 说明 | 适配要点 |
|-------|------|---------|
| `input/output` | 定义输入输出张量 | 修改维度/数据类型 |
| `parameters` | 运行时参数 | 添加/删除自定义参数 |
| `instance_group` | 部署配置 | 调整GPU数量/ID |
| `optional: true` | 可选输入 | 标记非必需输入 |


### 3. 🧪 `test_client.py` - 客户端测试文件

#### 输入输出配置说明


本测试客户端支持多种输入数据类型，你需要根据模型的实际输入要求进行调整：

1. 图像输入​​：通过compress_image()函数自动将图像转为JPEG格式，需确保输入图像为HWC格式的numpy数组
2. 数值输入​​：直接传入numpy数组，注意数据类型需与模型要求一致（如float32/int32）
3. 可选输入​​：在config.pbtxt中标记为optional的输入可以不提供


输出层名称必须与模型配置完全匹配，建议通过以下方式确认：

1. 查看模型的config.pbtxt文件中的output定义
2. 或调用client.get_model_config()获取输出规格


#### 使用示例

**基础测试流程**

在代码里，已经有比较详细的注释，下面说明简单的流程和工具：

```python
# 1. 初始化客户端
client = TritonInferenceClient(
    model_name="model",
    address="model-service.namespace.svc:8001",  # K8s服务地址
    output_names=["FOLLOW1_POS", "FOLLOW2_POS"],
    use_grpc=True
)

# 2. 准备测试数据
inputs = {
    "ACTION_FOLLOW1_POS": np.random.rand(7).astype(np.float32),
    "CAMERA_LEFT": generate_test_image()  # 生成测试图像
}

# 3. 执行测试推理
outputs = client.infer(inputs)
print(f"FOLLOW1动作序列: {outputs['FOLLOW1_POS'].shape}")
```

**图像处理工具**
```python
def generate_test_image():
    """生成测试图像数据（支持真实图片加载）"""
    # 方案1：随机生成图像
    test_img = np.random.randint(0, 256, [480, 640, 3], dtype=np.uint8)
    
    # 方案2：加载真实图片
    # test_img = cv2.imread("test.jpg")
    
    return compress_image(test_img)  # 自动压缩为JPEG字节流
```


## 🛠️ 简单添加自定义配置示例

### 1. ➕ 添加新参数
**步骤**：
1. 在`config.pbtxt`中添加参数声明：
   ```protobuf
   parameters {
     key: "new_param"
     value: { string_value: "${default_value}" }
   }
   ```
2. 在`model.py`的`initialize`中读取参数：
   ```python
   self.new_param = float(
       os.environ.get("NEW_PARAM") or 
       model_config["parameters"]["new_param"]["string_value"]
   )
   ```

### 2. 🔄 修改输入输出
**案例**：添加新摄像头输入
1. 在`config.pbtxt`中添加：
   ```protobuf
   input {
     name: "CAMERA_TOP"
     data_type: TYPE_STRING
     dims: [1]
     optional: true
   }
   ```
2. 在`model.py`的`execute`中处理新输入：
   ```python
   tensor = pb_utils.get_input_tensor_by_name(request, "CAMERA_TOP")
   if tensor:
       byte_data = tensor.as_numpy()[0]
       views["camera_top"] = decompress_image(byte_data)
   ```

### 3. ⚙️ 调整部署配置
（该项一般不需要调整）
```protobuf
# 使用2个GPU实例
instance_group [
  { kind: KIND_GPU, count: 1, gpus: [0] },
  { kind: KIND_GPU, count: 1, gpus: [1] }
]

# 或使用CPU部署
instance_group [
  { kind: KIND_CPU, count: 2 }
]
```

## 📂 文件存放规范
1. **稳定版本**：存放在`/examples/triton_backend/`目录下
   - ✅ 包含完整测试和文档的配置
2. **开发中版本**：存放在项目根目录`/workspace/`下
   - 🧪 用于实验性开发和调试
   - 📛 命名规范：`/workspace/{user_name}/triton_config/`

## 🏆 值得注意
1. **📊 参数传递优先级**：
   ```mermaid
   graph LR
   A[环境变量] -->|最高优先级| C[实际值]
   B[config.pbtxt] -->|次优先级| C
   ```

2. **🖼️ 图像处理优化**：
   - 使用JPEG压缩减少传输开销（当前示例使用`TYPE_STRING`）
   - 在GPU上执行图像解码（如需要）

3. **⏳ 历史记录管理**：
   - 通过`ArmActionHistory`类维护状态
   - 修改`max_length`调整历史窗口大小
