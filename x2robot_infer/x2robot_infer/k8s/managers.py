import os
import time
from kubernetes import client, config

# Triton 服务配置（从模块导入）
triton_ports = [
    client.V1ContainerPort(container_port=8000, name="http"),
    client.V1ContainerPort(container_port=8001, name="grpc"),
    client.V1ContainerPort(container_port=8002, name="metrics"),
]

triton_command = ["/bin/sh", "-c"]

triton_args = [
    "export http_proxy=http://***********:7890 && "
    "export https_proxy=http://***********:7890 && "
    "python3 /opt/tritonserver/scripts/k8s/download_checkpoint.py && "
    "tritonserver --model-repository=/workspace/backend"
]

triton_service_ports = [
    client.V1ServicePort(name="http", port=8000, target_port=8000, protocol="TCP"),
    client.V1ServicePort(name="grpc", port=8001, target_port=8001, protocol="TCP"),
    client.V1ServicePort(name="metrics", port=8002, target_port=8002, protocol="TCP"),
]


class DeploymentManager:
    def __init__(self):
        config.load_kube_config()
        self.apps_v1 = client.AppsV1Api()
        self.core_v1 = client.CoreV1Api()

    def create_deployment(self, config_data):
        """创建Deployment对象"""
        required_fields = ["name", "image", "backend_path", "env_vars"]
        self._validate_config(config_data, required_fields)

        env_vars = self._prepare_env_vars(config_data)
        container = self._create_container(config_data, env_vars)
        volumes = self._create_volumes(config_data)

        return client.V1Deployment(
            api_version="apps/v1",
            kind="Deployment",
            metadata=client.V1ObjectMeta(name=config_data["name"], namespace=config_data.get("namespace", "default")),
            spec=client.V1DeploymentSpec(
                replicas=1,
                selector=client.V1LabelSelector(match_labels={"app": config_data["name"]}),
                template=client.V1PodTemplateSpec(
                    metadata=client.V1ObjectMeta(labels={"app": config_data["name"]}),
                    spec=client.V1PodSpec(containers=[container], volumes=volumes, runtime_class_name="nvidia"),
                ),
            ),
        )

    def create_service(self, config_data):
        """创建ClusterIP Service对象"""
        return client.V1Service(
            metadata=client.V1ObjectMeta(
                name=f"{config_data['name']}-service", labels={"app": config_data["name"]}, namespace=config_data.get("namespace", "default")
            ),
            spec=client.V1ServiceSpec(
                type="ClusterIP",
                selector={"app": config_data["name"]},
                ports=triton_service_ports,
            ),
        )

    def deploy_resources(self, deployment, service):
        """执行部署操作"""
        namespace = deployment.metadata.namespace

        print("\n[1/3] 正在创建Deployment...")
        self.apps_v1.create_namespaced_deployment(body=deployment, namespace=namespace)

        print("[2/3] 正在创建Service...")
        self.core_v1.create_namespaced_service(body=service, namespace=namespace)

    def health_check(self, config_data, timeout=300):
        """健康检查入口"""
        print("\n[3/3] 开始健康检查...")
        deploy_name = config_data["name"]
        namespace = config_data.get("namespace", "default")
        service_name = f"{deploy_name}-service"

        start_time = time.time()
        while time.time() - start_time < timeout:
            if self._check_service_ready(namespace, service_name):
                print("\n✅ 服务已就绪")
                return True

            if backoff_pods := self._check_pod_backoff(namespace, deploy_name):
                self._handle_backoff_pods(backoff_pods, namespace)
                return False

            time.sleep(5)
            print(f"⏳ 服务状态检查中... 已等待 {int(time.time()-start_time)} 秒")

        print("\n❌ 错误：服务部署超时")
        return False

    def _validate_config(self, config_data, required_fields):
        """配置验证逻辑"""
        for field in required_fields:
            if field not in config_data:
                raise ValueError(f"缺少必要字段: {field}")

        download_fields = ["download_url", "download_path"]
        if sum(1 for f in download_fields if f in config_data) != 1:
            raise ValueError("必须且只能指定一个下载源")

    def _prepare_env_vars(self, config_data):
        """准备环境变量"""
        env_vars = [client.V1EnvVar(k, v) for k, v in config_data["env_vars"].items()]

        download_type = "DOWNLOAD_URL" if "download_url" in config_data else "DOWNLOAD_PATH"
        download_value = config_data.get("download_url") or config_data.get("download_path")
        file_extension = os.path.splitext(download_value)[1]

        env_vars.extend(
            [
                client.V1EnvVar(name="CKPT_PATH", value=f"/workspace/ckpt/{os.path.basename(config_data['name'])}{file_extension}"),
                client.V1EnvVar(name=download_type, value=download_value),
                client.V1EnvVar(name="http_proxy", value="***********:7890"),
                client.V1EnvVar(name="https_proxy", value="***********:7890"),
            ]
        )
        return env_vars

    def _create_container(self, config_data, env_vars):
        """创建容器定义"""
        if "vram_limit" in config_data and config_data["vram_limit"] != 0:
            resources = client.V1ResourceRequirements(
                limits={"nvidia.com/gpu": "1", "nvidia.com/gpumem": config_data["vram_limit"]},
            )
        else:
            resources = client.V1ResourceRequirements(
                requests={"nvidia.com/gpu": "1"},
                limits={"nvidia.com/gpu": "1"},
            )
        return client.V1Container(
            name=config_data["name"],
            image=config_data["image"],
            env=env_vars,
            ports=triton_ports,
            command=triton_command,
            args=triton_args,
            volume_mounts=[
                client.V1VolumeMount(name="backend-volume", mount_path="/workspace/backend"),
                client.V1VolumeMount(name="ckpt-volume", mount_path="/workspace/ckpt"),
                client.V1VolumeMount(name="ssh-public-key-volume", mount_path="/root/.ssh/id_rsa.pub", sub_path="id_rsa.pub", read_only=True),
                client.V1VolumeMount(name="ssh-private-key-volume", mount_path="/root/.ssh/id_rsa", sub_path="id_rsa", read_only=True),
            ],
            resources=resources,
            liveness_probe=client.V1Probe(
                http_get=client.V1HTTPGetAction(port=8000, path="/v2/health/ready"),
                initial_delay_seconds=18000,
                period_seconds=10,
                timeout_seconds=5,
                failure_threshold=3,
            ),
            readiness_probe=client.V1Probe(
                http_get=client.V1HTTPGetAction(port=8000, path="/v2/health/ready"),
                initial_delay_seconds=20,
                period_seconds=3,
                timeout_seconds=3,
                success_threshold=1,
            ),
        )

    def _create_volumes(self, config_data):
        """创建存储卷定义"""
        backend_path = config_data["backend_path"]
        if not backend_path.startswith("/mnt/backends"):
            print(f"[Warning] backend_path must start with '/mnt/backends', got '{backend_path}'")
            return [
                client.V1Volume(name="backend-volume", host_path=client.V1HostPathVolumeSource(path=config_data["backend_path"], type="Directory")),
                client.V1Volume(
                    name="ckpt-volume", nfs=client.V1NFSVolumeSource(server="************", path="/mnt/infra/k8s_share/ckpts", read_only=False)
                ),
                client.V1Volume(
                    name="ssh-public-key-volume",
                    config_map=client.V1ConfigMapVolumeSource(
                        name="ssh-public-key-config", items=[client.V1KeyToPath(key="id_rsa.pub", path="id_rsa.pub")]
                    ),
                ),
                client.V1Volume(
                    name="ssh-private-key-volume",
                    secret=client.V1SecretVolumeSource(
                        secret_name="ssh-key-secret", items=[client.V1KeyToPath(key="id_rsa", path="id_rsa")], default_mode=0o600
                    ),
                ),
            ]
        else:
            # 提取相对路径部分（移除开头的 /mnt/backends）
            relative_path = backend_path[len("/mnt/backends") :]

            # 规范化路径（确保以 / 开头且没有多余的斜杠）
            relative_path = relative_path.strip("/")
            if relative_path:
                relative_path = "/" + relative_path

            return [
                # CKPT 卷 - NFS 挂载
                client.V1Volume(
                    name="ckpt-volume", nfs=client.V1NFSVolumeSource(server="************", path="/mnt/infra/k8s_share/ckpts", read_only=False)
                ),
                # Backend 卷 - NFS 挂载 (修改为/mnt/backends路径)
                client.V1Volume(
                    name="backend-volume",
                    nfs=client.V1NFSVolumeSource(server="************", path="/mnt/infra/k8s_share/backends" + relative_path, read_only=False),
                ),
                # SSH 配置保持不变
                client.V1Volume(
                    name="ssh-public-key-volume",
                    config_map=client.V1ConfigMapVolumeSource(
                        name="ssh-public-key-config", items=[client.V1KeyToPath(key="id_rsa.pub", path="id_rsa.pub")]
                    ),
                ),
                client.V1Volume(
                    name="ssh-private-key-volume",
                    secret=client.V1SecretVolumeSource(
                        secret_name="ssh-key-secret", items=[client.V1KeyToPath(key="id_rsa", path="id_rsa")], default_mode=0o600
                    ),
                ),
            ]

    def _check_service_ready(self, namespace, service_name):
        """检查服务就绪状态"""
        try:
            endpoint = self.core_v1.read_namespaced_endpoints(service_name, namespace)

            # 处理 subsets 为 None 的情况
            if not endpoint.subsets:
                return False

            # 双重验证：1.存在有效子集 2.所有子集包含必要信息
            return all(subset.addresses and subset.ports for subset in endpoint.subsets)
        except client.rest.ApiException:
            return False

    def _check_pod_backoff(self, namespace, deploy_name):
        """检测Backoff状态Pod"""
        try:
            deployment = self.apps_v1.read_namespaced_deployment(deploy_name, namespace)
            selector = ",".join(f"{k}={v}" for k, v in deployment.spec.selector.match_labels.items())

            return [
                (pod.metadata.name, cs.state.waiting.reason, cs.name)
                for pod in self.core_v1.list_namespaced_pod(namespace, label_selector=selector).items
                for cs in (pod.status.container_statuses or [])
                if cs.state.waiting and "BackOff" in cs.state.waiting.reason
            ]
        except client.rest.ApiException:
            return []

    def _handle_backoff_pods(self, backoff_pods, namespace):
        """处理异常Pod"""
        print("\n⚠️ 检测到异常Pod：")
        for pod_name, reason, container_name in backoff_pods:
            print(f"  名称: {pod_name} | 状态: {reason}")
            print("=" * 50)
            print(self._get_pod_logs(namespace, pod_name, container_name))
            print("=" * 50 + "\n")

    def _get_pod_logs(self, namespace, pod_name, container_name):
        """获取Pod日志"""
        try:
            return self.core_v1.read_namespaced_pod_log(name=pod_name, namespace=namespace, container=container_name, tail_lines=50)
        except client.rest.ApiException as e:
            return f"获取日志失败: {e.reason}"

    def update_deployment_and_wait(self, config_data, timeout=300):
        """更新部署并等待滚动更新完成"""
        deploy_name = config_data["name"]
        namespace = config_data.get("namespace", "default")

        # 获取当前部署
        current_deployment = self.apps_v1.read_namespaced_deployment(name=deploy_name, namespace=namespace)

        # 准备新环境变量
        env_vars = self._prepare_env_vars(config_data)
        new_container = self._create_container(config_data, env_vars)

        # 更新部署
        current_deployment.spec.template.spec.containers = [new_container]

        print("\n[1/3] 正在更新Deployment...")
        self.apps_v1.patch_namespaced_deployment(name=deploy_name, namespace=namespace, body=current_deployment)

        print("[2/3] 等待滚动更新完成...")
        if not self._wait_for_rollout(deploy_name, namespace, timeout):
            print("\n❌ 错误：滚动更新超时")
            return False

        print("[3/3] 验证服务可用性...")
        return self.health_check(config_data, timeout // 2)  # 使用一半的超时时间进行健康检查

    def _wait_for_rollout(self, deploy_name, namespace, timeout):
        """等待滚动更新完成"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            deployment = self.apps_v1.read_namespaced_deployment(name=deploy_name, namespace=namespace)

            # 检查更新状态
            status = deployment.status
            spec = deployment.spec

            # 检查条件：1. 副本数匹配 2. 更新版本匹配 3. 没有旧副本在运行
            if (
                status.updated_replicas == spec.replicas
                and status.replicas == spec.replicas
                and status.available_replicas == spec.replicas
                and status.observed_generation >= deployment.metadata.generation
            ):
                return True

            time.sleep(5)
            print(f"⏳ 滚动更新中... 已等待 {int(time.time()-start_time)} 秒")

        return False
