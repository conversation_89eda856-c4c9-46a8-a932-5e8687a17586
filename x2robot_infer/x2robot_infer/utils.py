import numpy as np
from collections import deque
from scipy.signal import butter, filtfilt
import torch
class ArmActionHistory:
    def __init__(self, max_length=20):
        self.history = deque(maxlen=max_length)
    
    def add_action(self, action):
        """
        添加一个新的动作到历史记录中
        """
        self.history.append(action)
    
    def get_history(self):
        """
        获取所有的动作历史列表
        """
        hist = list(self.history)
        # print(f'hist: {hist}')
        hist = np.array(hist)
        return hist
    
    def __len__(self):
        """
        返回当前历史记录中的动作数量
        """
        return len(self.history)


def butterworth_lowpass_filter(data:np.ndarray, cutoff_freq:float = 0.5, sampling_freq:float = 15.0, order=2) -> np.ndarray:
    """
    Applies a low-pass Butterworth filter to the input data.
    
    Parameters:
        data (np.array): Input data array.
        cutoff (float): Cutoff frequency of the filter (Hz). Smoother for lower values.
        fs (float): Sampling frequency of the data (Hz).
        order (int): Order of the filter. Higher order may introduce phase distortions.
        
    Returns:
        filtered_data (np.array): Filtered data array with same shape as data.
    """
    nyquist = 0.5 * sampling_freq
    normal_cutoff = cutoff_freq / nyquist
    b, a = butter(order, normal_cutoff, btype='low', analog=False)

    # apply the filter along axis 0
    filtered_data = filtfilt(b, a, data, axis=0)
    return filtered_data

def smoothen_actions(actions: np.ndarray) -> np.ndarray:
    """
    Smoothens the provided action sequence 
    Args:
        actions (np.ndarray): actions from policy
    """
    if not isinstance(actions, np.ndarray):
        raise ValueError(f"Invalid input type for actions {type(actions)}. Expected np.ndarray!")

    if len(actions.shape) == 3 and not actions.shape[0] == 1:
        raise NotImplementedError(f"Batch processing not implemented!!")

    # apply the low-pass filter
    actions_smoothed = butterworth_lowpass_filter(actions)
    return actions_smoothed