import os

os.environ["CUDA_VISIBLE_DEVICES"] = "1"
device = "cuda:0"

import socket
import time
import json
import numpy as np
import cv2
import struct
import yaml
import torch
import argparse
import jsonlines
import numpy as np
import torch
import torch.nn as nn
from diffusers.schedulers.scheduling_ddpm import DDPMScheduler
from diffusers import LCMScheduler
import matplotlib.pyplot as plt
import cv2
import numpy as np
from scipy.spatial.transform import Rotation as R
from diffusers.training_utils import EMAModel

from .diffusion_policy_cnn_model_utils import (
    get_resnet,
    replace_bn_with_gn,
    ConditionalUnet1D,
)

from transformers import AutoTokenizer, CLIPTextModel
import torch.nn as nn
import os

os.environ["TOKENIZERS_PARALLELISM"] = "false"


class CLIP_Text_Encoder(nn.Module):
    def __init__(self, model_path="/x2robot/liangyuxin/Models/openai_clip-vit-base-patch32", all_freeze=True) -> None:
        super().__init__()
        self.model = CLIPTextModel.from_pretrained(model_path)
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        self.model.to("cuda")
        self.model.eval()
        for param in self.model.parameters():
            param.requires_grad = False
        if not all_freeze:
            print("🔥Unfreeze the last layer of CLIP text encoder🔥", flush=True)
            for param in self.model.text_model.encoder.layers[-1].parameters():
                param.requires_grad = True
            for param in self.model.text_model.final_layer_norm.parameters():
                param.requires_grad = True

    def encode_text(self, texts):
        inputs = self.tokenizer(texts, padding=True, return_tensors="pt", truncation=True, max_length=64)
        inputs = {"input_ids": inputs["input_ids"].to(self.model.device), "attention_mask": inputs["attention_mask"].to(self.model.device)}
        outputs = self.model(**inputs)
        text_features = outputs.pooler_output  # pooled (EOS token) states

        return text_features


import torchvision.transforms as T
from einops import rearrange
from PIL import Image

# print("####### 💫 using og transform 💫 #######",flush=True)
# nn_transform = torch.nn.Sequential(
#     T.Resize(size=235, interpolation=T.InterpolationMode("bicubic")),
#     T.CenterCrop(size=(224, 224)),
#     T.Normalize(mean=torch.tensor([0.4850, 0.4560, 0.4060]), std=torch.tensor([0.2290, 0.2240, 0.2250])),
# )

# print("####### 💫 using new transform 💫 #######",flush=True)
# nn_transform = torch.nn.Sequential(
#     T.Resize(512, interpolation=T.InterpolationMode.BILINEAR),
#     T.CenterCrop(512),
#     # T.RandomApply([T.GaussianBlur(kernel_size=5)], p=0.2),
#     # T.RandomApply([T.ColorJitter(brightness=0.4, contrast=0.4, saturation=0.4, hue=0.1)], p=0.2),
#     T.Resize(size=235, interpolation=T.InterpolationMode("bicubic")),
#     T.CenterCrop(size=(224, 224)),
#     T.Normalize([0.5], [0.5]),
# )

print("####### 💫 using newest transform 💫 #######", flush=True)
nn_transform = torch.nn.Sequential(
    # T.RandomApply([T.ColorJitter(brightness=0.4, contrast=0.4, saturation=0.4, hue=0.1)], p=0.1),
    T.Resize(512, interpolation=T.InterpolationMode.BILINEAR),
    # T.RandomCrop(size=(512, 512)),
    T.CenterCrop(size=(512, 512)),
    T.Normalize([0.5], [0.5]),
)

data_config = {
    "input_size": (3, 224, 224),
    "interpolation": "bicubic",
    "mean": (0.485, 0.456, 0.406),
    "std": (0.229, 0.224, 0.225),
    "crop_pct": 0.95,
    "crop_mode": "center",
}


def transform_images(images):
    # (1, n, h, w, c)
    if not isinstance(images, torch.Tensor):
        images = torch.tensor(images)
    images = images.to(device)
    if images.shape[-1] == 3:
        images = images.permute(0, 3, 1, 2)
    images = torch.tensor(images / 255.0, dtype=torch.float)
    images = nn_transform(images)
    return images


class Normalizer(nn.Module):
    def __init__(self, action_dim):
        super(Normalizer, self).__init__()
        self.min = nn.Parameter(torch.zeros(action_dim), requires_grad=False)
        self.delta = nn.Parameter(torch.ones(action_dim), requires_grad=False)

    def normalize_data(self, x):
        x = (x - self.min) / (self.delta)
        x = x * 2 - 1
        return x

    def unnormalize_data(self, x):
        x = (x + 1) / 2
        x = x * self.delta + self.min
        return x


class DP_Infer_model:
    def __init__(
        self,
        config,
        ckpt_path,
        clip_path="/home/<USER>/Models/openai_clip-vit-base-patch32",
        use_ema=False,
        instruction=None,
        goal_image=None,
        action_start_ratio=0.25,
        action_end_ratio=1.0,
    ):
        super().__init__()
        self.history_images = None
        self.history_pos = None
        self.instruction = instruction
        self.use_ema = use_ema
        self.action_queue = []
        self.goal_image = goal_image
        self.action_start_ratio = action_start_ratio
        self.action_end_ratio = action_end_ratio
        self.config = config
        self.load_model(config, ckpt_path, clip_path=clip_path)

        self.bi_arm = True if (self.action_dim >= 14) else False
        # print("instruction:",self.instruction)

    def predict_action(self, obs_dict, goal_image=None, batch_choose=False):
        org_agent_pos = obs_dict["agent_pos"].to(device)
        agent_pos = org_agent_pos[:, :, : self.action_dim]
        agent_pos = self.nets["normalizer"].normalize_data(agent_pos)
        if org_agent_pos.shape[-1] > agent_pos.shape[-1]:
            agent_pos = torch.cat([agent_pos, org_agent_pos[:, :, self.action_dim :]], dim=-1)

        multi_view = torch.concat([obs_dict[k] for k in self.camera_names], axis=1).squeeze(0)
        images = transform_images(multi_view)

        B = agent_pos.shape[0]
        image_features = self.nets["vision_encoder"](images)
        image_features = image_features.reshape(B, self.obs_horizon, -1)
        obs_features = image_features
        # print("obs_features.shape",obs_features.shape,flush=True)
        if self.instruction is not None:
            instructions = self.instruction
            text_features = self.nets["text_encoder"].encode_text(instructions)
            text_features = self.nets["text_feature_proj"](text_features)
            text_features = text_features.reshape(B, self.obs_horizon, -1)
            obs_features = torch.cat([obs_features, text_features], dim=-1)

        elif self.goal_image is not None or goal_image is not None:
            goal_image = self.goal_image if goal_image is None else goal_image
            goal_image = torch.tensor(goal_image).unsqueeze(0)
            print("goal_image.shape", goal_image.shape, flush=True)
            goal_image = transform_images(goal_image)
            goal_image_features = self.nets["vision_encoder"](goal_image)
            goal_image_features = self.nets["goal_image_proj"](goal_image_features)
            goal_image_features = goal_image_features.reshape(B, self.obs_horizon, -1)  # (B,obs_horizon,D)
            obs_features = torch.cat([obs_features, goal_image_features], dim=-1)

        if self.use_history:
            naction_history = self.history_pos[: self.history_len].cuda().unsqueeze(0)
            naction_history = rearrange(naction_history, "b l d -> b (l d)")
            naction_history = self.nets["history_encoder"](naction_history).reshape(B, self.obs_horizon, -1)
            obs_features = torch.cat([obs_features, naction_history], dim=-1)

        if not self.not_use_pos:
            obs_features = torch.cat([obs_features, agent_pos], dim=-1)
        obs_cond = obs_features.flatten(start_dim=1).float()  # (B, obs_horizon * obs_dim)
        # obs_cond = self.nets["condition_proj"](obs_cond)
        if batch_choose:
            batch_size = 16
            print("obs_cond.shape", obs_cond.shape, flush=True)
            obs_cond = obs_cond.repeat(batch_size, 1)
            print("obs_cond.shape", obs_cond.shape, flush=True)
            noisy_action = torch.randn((B * batch_size, self.pred_horizon, self.action_dim), device=device)
        else:
            noisy_action = torch.randn((B, self.pred_horizon, self.action_dim), device=device)
        naction = noisy_action

        self.noise_scheduler.set_timesteps(self.num_diffusion_iters)

        for k in self.noise_scheduler.timesteps:
            # predict noise
            noise_pred = self.nets["noise_pred_net"](sample=naction, timestep=k, global_cond=obs_cond)

            # inverse diffusion step (remove noise)
            naction = self.noise_scheduler.step(model_output=noise_pred, timestep=k, sample=naction).prev_sample

        # unnormalize action
        naction = naction
        if self.use_history:
            self.add_history(naction)
        # action_pred = unnormalize_data(naction)
        action_pred = self.nets["normalizer"].unnormalize_data(naction).detach().to("cpu")
        if batch_choose:
            print("action_pred.shape", action_pred.shape, flush=True)
            # action_pred = action_pred[:1]
            shape = action_pred.shape
            action_pred = action_pred.mean(axis=0).reshape(1, shape[1], shape[2])
            print("action_pred.shape", action_pred.shape, flush=True)

        return {"action_pred": action_pred}

    def load_model(self, config, ckpt_path, clip_path):
        if "actor" in config:
            config = config["actor"]
        self.vision_feature_dim = config["vision_feature_dim"]
        self.lowdim_obs_dim = config["action_dim"]
        self.not_use_pos = "not_use_pos" in config and config["not_use_pos"]
        self.obs_dim = self.vision_feature_dim * config["camera_num"] + self.lowdim_obs_dim
        if self.not_use_pos:
            self.obs_dim = self.obs_dim - self.lowdim_obs_dim
        if config["use_instruction"]:
            self.obs_dim = self.obs_dim + config["text_feature_dim"]
        self.obs_horizon = config["obs_horizon"]
        self.action_dim = config["action_dim"]
        self.num_diffusion_iters = config["num_diffusion_iters"]
        self.pred_horizon = config["data"]["action_horizon"]
        self.camera_names = config["camera_names"]
        self.use_history = "use_history" in config and config["use_history"]
        if self.use_history:
            self.history_len = config["history_length"]
            history_dim = self.lowdim_obs_dim * self.history_len
            # print("use history",self.history_len,"history_dim",history_dim)
            history_encoder = nn.Linear(history_dim, 64)  # HARDCODED
            self.obs_dim = self.obs_dim + 64
            # history_pos = normalize_data(torch.zeros((self.history_len,self.lowdim_obs_dim)))
        if self.config["data"].get("use_gripper_cur", False):
            self.obs_dim += 2

        # print(ckpt_path,flush=True)
        checkpoint = torch.load(ckpt_path, map_location="cpu")
        resnet_name = config["resnet_name"] if "resnet_name" in config else "resnet18"
        vision_encoder = get_resnet(resnet_name)
        vision_encoder = replace_bn_with_gn(vision_encoder)
        noise_pred_net = ConditionalUnet1D(
            input_dim=self.action_dim,
            down_dims=config["down_dims"] if "down_dims" in config else [256, 512, 1024],
            global_cond_dim=self.obs_dim * self.obs_horizon,
        )
        if config["use_instruction"]:
            text_encoder = CLIP_Text_Encoder(model_path=clip_path)
        else:
            text_encoder = None

        # the final arch has 2 parts
        self.nets = nn.ModuleDict(
            {
                "vision_encoder": vision_encoder,
                "text_encoder": text_encoder,
                "text_feature_proj": nn.Linear(512, config["text_feature_dim"]) if config["use_instruction"] else None,
                "goal_image_proj": nn.Linear(512, config.get("text_feature_dim", 256)),
                "noise_pred_net": noise_pred_net,
                # "condition_proj": condition_proj,
                "history_encoder": history_encoder if self.use_history else None,
                "normalizer": Normalizer(self.action_dim),
            }
        )
        if "module.vision_encoder.conv1.weight" in checkpoint["model_state_dict"]:
            checkpoint["model_state_dict"] = {k[len("module.") :]: v for k, v in checkpoint["model_state_dict"].items()}
        # self.nets.load_state_dict(checkpoint['model_state_dict'], strict=True)
        err = self.nets.load_state_dict(checkpoint["model_state_dict"], strict=False)
        print(err)
        self.nets.to(device)
        if self.use_ema:
            # print("using ema")
            self.ema = EMAModel(
                parameters=self.nets.parameters(),
                power=0.75,
            )
            self.ema.load_state_dict(checkpoint["ema_state_dict"])
            self.ema.store(self.nets.parameters())
            self.ema.copy_to(self.nets.parameters())

        self.nets.eval()

        self.noise_scheduler = DDPMScheduler(
            num_train_timesteps=self.num_diffusion_iters, beta_schedule="squaredcos_cap_v2", clip_sample=True, prediction_type="epsilon"
        )
        # noise_scheduler = LCMScheduler.from_config(self.noise_scheduler.config)
        # self.noise_scheduler = noise_scheduler
        if self.use_history:
            history_pos = self.nets["normalizer"].normalize_data(torch.zeros((self.history_len, self.lowdim_obs_dim)).to(device))
            self.add_history(history_pos)

    def add_history(self, pos_history):
        if self.history_pos is None:
            self.history_pos = pos_history
        else:
            start_frame = int(self.action_start_ratio * pos_history.shape[0])
            end_frame = int(self.action_end_ratio * pos_history.shape[0])
            pos_history = pos_history[start_frame:end_frame]
            self.history_pos = torch.cat([self.history_pos, pos_history[0]], axis=0)
        if self.history_pos.shape[0] > self.history_len:
            self.history_pos = self.history_pos[-(self.history_len) :]
