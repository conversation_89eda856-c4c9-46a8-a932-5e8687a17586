from setuptools import setup, find_packages

setup(
    name="x2robot_infer",
    version="0.1.0",
    packages=find_packages(),
    
    # 显式包含数据文件
    package_data={
        'x2robot_infer': [
            'eval/*',
            'models/*',
            'backend/*'
        ]
    },
    include_package_data=True,
    install_requires=[],
    description="X2 Robot Inference Toolkit",
    classifiers=[
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Operating System :: OS Independent",
    ],
)